"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const update_profile_dto_1 = require("./dto/update-profile.dto");
const wallet_deposit_dto_1 = require("./dto/wallet-deposit.dto");
const notification_query_dto_1 = require("./dto/notification-query.dto");
const checkin_history_query_dto_1 = require("./dto/checkin-history-query.dto");
const coupon_query_dto_1 = require("./dto/coupon-query.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let UsersController = class UsersController {
    usersService;
    constructor(usersService) {
        this.usersService = usersService;
    }
    getProfile(user) {
        return this.usersService.getProfile(user.id);
    }
    updateProfile(user, updateProfileDto) {
        return this.usersService.updateProfile(user.id, updateProfileDto);
    }
    getPoints(user) {
        return this.usersService.getPoints(user.id);
    }
    checkin(user) {
        return this.usersService.checkin(user.id);
    }
    getCheckinHistory(user, query) {
        return this.usersService.getCheckinHistory(user.id, query);
    }
    getNotifications(user, query) {
        return this.usersService.getNotifications(user.id, query);
    }
    markNotificationAsRead(user, notificationId) {
        return this.usersService.markNotificationAsRead(user.id, notificationId);
    }
    markAllNotificationsAsRead(user) {
        return this.usersService.markAllNotificationsAsRead(user.id);
    }
    deleteNotification(user, notificationId) {
        return this.usersService.deleteNotification(user.id, notificationId);
    }
    getWallet(user) {
        return this.usersService.getWallet(user.id);
    }
    depositToWallet(user, depositDto) {
        return this.usersService.depositToWallet(user.id, depositDto);
    }
    getCoupons(user, query) {
        return this.usersService.getCoupons(user.id, query);
    }
};
__decorate([
    (0, common_1.Get)('profile'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Put)('profile'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_profile_dto_1.UpdateProfileDto]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "updateProfile", null);
__decorate([
    (0, common_1.Get)('points'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "getPoints", null);
__decorate([
    (0, common_1.Post)('checkin'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "checkin", null);
__decorate([
    (0, common_1.Get)('checkin-history'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, checkin_history_query_dto_1.CheckinHistoryQueryDto]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "getCheckinHistory", null);
__decorate([
    (0, common_1.Get)('notifications'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, notification_query_dto_1.NotificationQueryDto]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "getNotifications", null);
__decorate([
    (0, common_1.Put)('notifications/:notificationId/read'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('notificationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "markNotificationAsRead", null);
__decorate([
    (0, common_1.Put)('notifications/read-all'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "markAllNotificationsAsRead", null);
__decorate([
    (0, common_1.Delete)('notifications/:notificationId'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('notificationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "deleteNotification", null);
__decorate([
    (0, common_1.Get)('wallet'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "getWallet", null);
__decorate([
    (0, common_1.Post)('wallet/deposit'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, wallet_deposit_dto_1.WalletDepositDto]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "depositToWallet", null);
__decorate([
    (0, common_1.Get)('coupons'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, coupon_query_dto_1.CouponQueryDto]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "getCoupons", null);
UsersController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
exports.UsersController = UsersController;
//# sourceMappingURL=users.controller.js.map