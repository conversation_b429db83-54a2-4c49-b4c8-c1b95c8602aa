"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const config_1 = require("@nestjs/config");
let PrismaService = class PrismaService {
    configService;
    prisma;
    constructor(configService) {
        this.configService = configService;
        const url = this.configService.get('MOBILE_API_DATABASE_URL');
        this.prisma = new client_1.PrismaClient({
            datasources: {
                db: {
                    url,
                },
            },
        });
    }
    async onModuleInit() {
        await this.prisma.$connect();
    }
    async onModuleDestroy() {
        await this.prisma.$disconnect();
    }
    get user() {
        return this.prisma.user;
    }
    get yollooCard() {
        return this.prisma.yollooCard;
    }
    get esim() {
        return this.prisma.esim;
    }
    get product() {
        return this.prisma.product;
    }
    get category() {
        return this.prisma.category;
    }
    get productVariant() {
        return this.prisma.productVariant;
    }
    get cartItem() {
        return this.prisma.cartItem;
    }
    get order() {
        return this.prisma.order;
    }
    get orderItem() {
        return this.prisma.orderItem;
    }
    get review() {
        return this.prisma.review;
    }
    get notification() {
        return this.prisma.notification;
    }
    get wallet() {
        return this.prisma.wallet;
    }
    get transaction() {
        return this.prisma.transaction;
    }
    get paymentCard() {
        return this.prisma.paymentCard;
    }
    get coupon() {
        return this.prisma.coupon;
    }
    get userCoupon() {
        return this.prisma.userCoupon;
    }
    get packageUsage() {
        return this.prisma.packageUsage;
    }
    get page() {
        return this.prisma.page;
    }
    get article() {
        return this.prisma.article;
    }
    get banner() {
        return this.prisma.banner;
    }
    get homeFeature() {
        return this.prisma.homeFeature;
    }
    get travelTip() {
        return this.prisma.travelTip;
    }
    get continent() {
        return this.prisma.continent;
    }
    get country() {
        return this.prisma.country;
    }
    get mobileOperator() {
        return this.prisma.mobileOperator;
    }
    get rechargeHistory() {
        return this.prisma.rechargeHistory;
    }
    get refund() {
        return this.prisma.refund;
    }
    get productView() {
        return this.prisma.productView;
    }
    get socialAccount() {
        return this.prisma.socialAccount;
    }
    get upload() {
        return this.prisma.upload;
    }
};
PrismaService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], PrismaService);
exports.PrismaService = PrismaService;
//# sourceMappingURL=prisma.service.js.map