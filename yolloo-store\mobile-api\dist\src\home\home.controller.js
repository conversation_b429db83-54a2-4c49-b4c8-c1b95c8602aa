"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeController = void 0;
const common_1 = require("@nestjs/common");
const home_service_1 = require("./home.service");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const request_context_decorator_1 = require("../common/decorators/request-context.decorator");
const home_query_dto_1 = require("./dto/home-query.dto");
const banner_query_dto_1 = require("./dto/banner-query.dto");
const travel_tips_query_dto_1 = require("./dto/travel-tips-query.dto");
const recommendations_query_dto_1 = require("./dto/recommendations-query.dto");
const nearby_guides_query_dto_1 = require("./dto/nearby-guides-query.dto");
const public_decorator_1 = require("../common/decorators/public.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let HomeController = class HomeController {
    homeService;
    constructor(homeService) {
        this.homeService = homeService;
    }
    getHomeData(user, query, ctx) {
        const userId = user?.id || null;
        return this.homeService.getHomeData(userId, query, ctx);
    }
    getBanners(user, query, ctx) {
        const userId = user?.id || null;
        return this.homeService.getBanners(userId, query, ctx);
    }
    getTravelTips(user, query, ctx) {
        const userId = user?.id || null;
        return this.homeService.getTravelTips(userId, query, ctx);
    }
    getRecommendations(user, query, ctx) {
        const userId = user?.id || null;
        return this.homeService.getRecommendations(userId, query, ctx);
    }
    getNearbyGuides(query, ctx) {
        return this.homeService.getNearbyGuides(query, ctx);
    }
};
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('home'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, home_query_dto_1.HomeQueryDto, Object]),
    __metadata("design:returntype", void 0)
], HomeController.prototype, "getHomeData", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('home/banners'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, banner_query_dto_1.BannerQueryDto, Object]),
    __metadata("design:returntype", void 0)
], HomeController.prototype, "getBanners", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('home/travel-tips'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, travel_tips_query_dto_1.TravelTipsQueryDto, Object]),
    __metadata("design:returntype", void 0)
], HomeController.prototype, "getTravelTips", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('home/recommendations'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, recommendations_query_dto_1.RecommendationsQueryDto, Object]),
    __metadata("design:returntype", void 0)
], HomeController.prototype, "getRecommendations", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('guides/nearby'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [nearby_guides_query_dto_1.NearbyGuidesQueryDto, Object]),
    __metadata("design:returntype", void 0)
], HomeController.prototype, "getNearbyGuides", null);
HomeController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [home_service_1.HomeService])
], HomeController);
exports.HomeController = HomeController;
//# sourceMappingURL=home.controller.js.map