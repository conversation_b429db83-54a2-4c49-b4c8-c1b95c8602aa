import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    createOrder(user: any, createOrderDto: CreateOrderDto): Promise<{
        orderId: string;
        paymentIntentId: string;
        clientSecret: string;
        total: number;
        currency: string;
        paymentUrl: string;
    }>;
    getOrderById(user: any, orderId: string): Promise<{
        id: string;
        status: import(".prisma/client").OrderStatus;
        paymentStatus: string;
        total: number;
        currency: string;
        items: {
            id: string;
            productCode: string;
            name: string;
            price: number;
            quantity: number;
            imageUrl: string;
        }[];
        esims: never[];
        createdAt: string;
        updatedAt: string;
    }>;
    getUserOrders(user: any, query: OrderQueryDto): Promise<{
        orders: {
            id: string;
            status: import(".prisma/client").OrderStatus;
            total: number;
            currency: string;
            items: {
                id: string;
                productCode: string;
                productName: string;
                quantity: number;
                price: number;
            }[];
            createdAt: string;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
    }>;
}
