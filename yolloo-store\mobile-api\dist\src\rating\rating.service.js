"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RatingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let RatingService = RatingService_1 = class RatingService {
    prisma;
    logger = new common_1.Logger(RatingService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async calculateProductRating(productId) {
        const reviews = await this.prisma.review.findMany({
            where: { productId },
            select: { rating: true },
        });
        if (reviews.length === 0) {
            return {
                averageRating: 0,
                totalReviews: 0,
                ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
            };
        }
        const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
        const averageRating = totalRating / reviews.length;
        const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
        reviews.forEach(review => {
            const rating = Math.round(review.rating);
            ratingDistribution[rating]++;
        });
        return {
            averageRating: Math.round(averageRating * 10) / 10,
            totalReviews: reviews.length,
            ratingDistribution,
        };
    }
    async getProductStatistics(productId) {
        const [reviews, orders, views] = await Promise.all([
            this.prisma.review.findMany({
                where: { productId },
                include: { user: { select: { name: true, image: true } } },
                orderBy: { createdAt: 'desc' },
            }),
            this.prisma.orderItem.count({
                where: { productId },
            }),
            this.prisma.productView.count({
                where: { productId },
            }),
        ]);
        const rating = await this.calculateProductRating(productId);
        return {
            ...rating,
            totalOrders: orders,
            totalViews: views,
            reviews: reviews.map(review => ({
                id: review.id,
                rating: review.rating,
                comment: review.comment,
                createdAt: review.createdAt,
                user: {
                    name: review.user?.name || 'Anonymous',
                    image: review.user?.image || '/images/defaults/user-avatar.jpg',
                },
            })),
        };
    }
    async updateProductPopularity(productId) {
        const stats = await this.getProductStatistics(productId);
        const popularityScore = this.calculatePopularityScore(stats.averageRating, stats.totalReviews, stats.totalOrders, stats.totalViews);
        await this.prisma.product.update({
            where: { id: productId },
            data: {
                popularityScore,
                isPopular: popularityScore > 70,
            },
        });
        return { popularityScore, isPopular: popularityScore > 70 };
    }
    calculatePopularityScore(averageRating, totalReviews, totalOrders, totalViews) {
        const ratingScore = (averageRating / 5) * 40;
        const reviewScore = Math.min(totalReviews / 100, 1) * 20;
        const orderScore = Math.min(totalOrders / 50, 1) * 30;
        const viewScore = Math.min(totalViews / 1000, 1) * 10;
        return Math.round(ratingScore + reviewScore + orderScore + viewScore);
    }
    async getTopRatedProducts(limit = 10, categoryId) {
        const where = {
            status: 'ACTIVE',
            off_shelve: false,
        };
        if (categoryId) {
            where.categoryId = categoryId;
        }
        const products = await this.prisma.product.findMany({
            where,
            include: {
                reviews: { select: { rating: true } },
                _count: {
                    select: {
                        orderItems: true,
                        reviews: true,
                    },
                },
            },
            take: limit * 2,
        });
        const productsWithRating = products
            .map(product => {
            const reviews = product.reviews;
            const averageRating = reviews.length > 0
                ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
                : 0;
            return {
                ...product,
                averageRating: Math.round(averageRating * 10) / 10,
                totalReviews: reviews.length,
                totalOrders: product._count.orderItems,
            };
        })
            .filter(product => product.totalReviews >= 5)
            .sort((a, b) => {
            if (b.averageRating !== a.averageRating) {
                return b.averageRating - a.averageRating;
            }
            return b.totalReviews - a.totalReviews;
        })
            .slice(0, limit);
        return productsWithRating;
    }
    async getMostPopularProducts(limit = 10, categoryId) {
        const where = {
            status: 'ACTIVE',
            off_shelve: false,
            isPopular: true,
        };
        if (categoryId) {
            where.categoryId = categoryId;
        }
        return await this.prisma.product.findMany({
            where,
            include: {
                reviews: { select: { rating: true } },
                _count: {
                    select: {
                        orderItems: true,
                        reviews: true,
                    },
                },
            },
            orderBy: [
                { popularityScore: 'desc' },
                { createdAt: 'desc' },
            ],
            take: limit,
        });
    }
    async recordProductView(productId, userId, ipAddress, userAgent) {
        try {
            await this.prisma.productView.create({
                data: {
                    productId,
                    userId,
                    ipAddress,
                    userAgent,
                },
            });
            this.updateProductPopularity(productId).catch(error => {
                this.logger.warn(`Failed to update popularity for product ${productId}:`, error);
            });
        }
        catch (error) {
            this.logger.warn(`Failed to record view for product ${productId}:`, error);
        }
    }
    async getUserRecommendations(userId, limit = 10) {
        const userOrders = await this.prisma.order.findMany({
            where: { userId },
            include: {
                items: {
                    include: {
                        product: {
                            select: {
                                id: true,
                                categoryId: true,
                                country: true,
                            },
                        },
                    },
                },
            },
        });
        const categoryPreferences = new Map();
        const countryPreferences = new Map();
        userOrders.forEach(order => {
            order.items.forEach(item => {
                if (item.product?.categoryId) {
                    categoryPreferences.set(item.product.categoryId, (categoryPreferences.get(item.product.categoryId) || 0) + 1);
                }
                if (item.product?.country) {
                    countryPreferences.set(item.product.country, (countryPreferences.get(item.product.country) || 0) + 1);
                }
            });
        });
        const preferredCategories = Array.from(categoryPreferences.keys()).slice(0, 3);
        const preferredCountries = Array.from(countryPreferences.keys()).slice(0, 3);
        const recommendations = await this.prisma.product.findMany({
            where: {
                status: 'ACTIVE',
                off_shelve: false,
                OR: [
                    { categoryId: { in: preferredCategories } },
                    { country: { in: preferredCountries } },
                    { isPopular: true },
                ],
            },
            include: {
                reviews: { select: { rating: true } },
                _count: {
                    select: { orderItems: true, reviews: true },
                },
            },
            orderBy: [
                { popularityScore: 'desc' },
                { createdAt: 'desc' },
            ],
            take: limit,
        });
        return recommendations.map(product => {
            const reviews = product.reviews;
            const averageRating = reviews.length > 0
                ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
                : 0;
            return {
                ...product,
                averageRating: Math.round(averageRating * 10) / 10,
                totalReviews: reviews.length,
                totalOrders: product._count.orderItems,
            };
        });
    }
    async batchUpdateProductPopularity() {
        const products = await this.prisma.product.findMany({
            where: {
                status: 'ACTIVE',
                off_shelve: false,
            },
            select: { id: true },
        });
        let updatedCount = 0;
        for (const product of products) {
            try {
                await this.updateProductPopularity(product.id);
                updatedCount++;
            }
            catch (error) {
                this.logger.warn(`Failed to update popularity for product ${product.id}:`, error);
            }
        }
        this.logger.log(`Updated popularity for ${updatedCount} products`);
        return { updatedCount, totalProducts: products.length };
    }
    async getProductRecommendations(productId, limit = 5) {
        const currentProduct = await this.prisma.product.findUnique({
            where: { id: productId },
            select: {
                categoryId: true,
                country: true,
                price: true,
            },
        });
        if (!currentProduct) {
            return [];
        }
        const similarProducts = await this.prisma.product.findMany({
            where: {
                id: { not: productId },
                status: 'ACTIVE',
                off_shelve: false,
                OR: [
                    { categoryId: currentProduct.categoryId },
                    { country: currentProduct.country },
                    {
                        price: {
                            gte: currentProduct.price * 0.8,
                            lte: currentProduct.price * 1.2,
                        },
                    },
                ],
            },
            include: {
                reviews: { select: { rating: true } },
                _count: {
                    select: { orderItems: true, reviews: true },
                },
            },
            orderBy: [
                { popularityScore: 'desc' },
                { createdAt: 'desc' },
            ],
            take: limit,
        });
        return similarProducts.map(product => {
            const reviews = product.reviews;
            const averageRating = reviews.length > 0
                ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
                : 0;
            return {
                ...product,
                averageRating: Math.round(averageRating * 10) / 10,
                totalReviews: reviews.length,
                totalOrders: product._count.orderItems,
            };
        });
    }
};
RatingService = RatingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RatingService);
exports.RatingService = RatingService;
//# sourceMappingURL=rating.service.js.map