"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let CartService = class CartService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getCart(userId) {
        const cartItems = await this.prisma.cartItem.findMany({
            where: { userId },
            include: {
                product: true,
                variant: true,
            },
        });
        if (cartItems.length === 0) {
            return {
                items: [],
                summary: {
                    subtotal: 0,
                    shipping: 0,
                    tax: 0,
                    total: 0,
                    currency: 'USD',
                },
            };
        }
        const items = cartItems.map(item => {
            const price = item.variant ? Number(item.variant.price) : item.product.price;
            const currency = item.variant ? item.variant.currency : 'USD';
            const specs = typeof item.product.specifications === 'string'
                ? JSON.parse(item.product.specifications)
                : item.product.specifications;
            const countries = specs?.countries || [];
            return {
                id: item.id,
                productId: item.productId,
                variantId: item.variantId,
                name: item.product.name,
                price: price,
                currency: currency,
                quantity: item.quantity,
                imageUrl: item.product.images && item.product.images.length > 0 ?
                    item.product.images[0] : 'https://example.com/default-product.jpg',
                attributes: {
                    dataSize: item.product.dataSize || 0,
                    duration: item.variant && item.variant.duration ? item.variant.duration : 30,
                    countries: countries,
                },
            };
        });
        const subtotal = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
        const shipping = 0;
        const tax = 0;
        const total = subtotal + shipping + tax;
        const currency = items[0].currency;
        return {
            items,
            summary: {
                subtotal,
                shipping,
                tax,
                total,
                currency,
            },
        };
    }
    async addCartItem(userId, addCartItemDto) {
        const { productId, variantId, quantity } = addCartItemDto;
        const product = await this.prisma.product.findUnique({
            where: { id: productId },
        });
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        if (variantId) {
            const variant = await this.prisma.productVariant.findUnique({
                where: { id: variantId },
            });
            if (!variant || variant.productId !== productId) {
                throw new common_1.NotFoundException('Product variant not found');
            }
        }
        const existingCartItem = await this.prisma.cartItem.findFirst({
            where: {
                userId,
                productId,
                variantId: variantId || null,
            },
        });
        let cartItem;
        if (existingCartItem) {
            cartItem = await this.prisma.cartItem.update({
                where: { id: existingCartItem.id },
                data: { quantity: existingCartItem.quantity + quantity },
            });
        }
        else {
            cartItem = await this.prisma.cartItem.create({
                data: {
                    userId,
                    productId,
                    variantId,
                    quantity,
                },
            });
        }
        return {
            id: cartItem.id,
            productId: cartItem.productId,
            variantId: cartItem.variantId,
            quantity: cartItem.quantity,
            message: '商品已添加到购物车',
        };
    }
    async updateCartItem(userId, itemId, updateCartItemDto) {
        const cartItem = await this.prisma.cartItem.findFirst({
            where: {
                id: itemId,
                userId,
            },
        });
        if (!cartItem) {
            throw new common_1.NotFoundException('Cart item not found');
        }
        const updatedCartItem = await this.prisma.cartItem.update({
            where: { id: itemId },
            data: { quantity: updateCartItemDto.quantity },
        });
        return {
            id: updatedCartItem.id,
            quantity: updatedCartItem.quantity,
            message: '购物车已更新',
        };
    }
    async removeCartItem(userId, itemId) {
        const cartItem = await this.prisma.cartItem.findFirst({
            where: {
                id: itemId,
                userId,
            },
        });
        if (!cartItem) {
            throw new common_1.NotFoundException('Cart item not found');
        }
        await this.prisma.cartItem.delete({
            where: { id: itemId },
        });
        return {
            message: '商品已从购物车中移除',
        };
    }
};
CartService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CartService);
exports.CartService = CartService;
//# sourceMappingURL=cart.service.js.map