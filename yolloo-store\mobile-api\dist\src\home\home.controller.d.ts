import { HomeService } from './home.service';
import { HomeQueryDto } from './dto/home-query.dto';
import { BannerQueryDto } from './dto/banner-query.dto';
import { TravelTipsQueryDto } from './dto/travel-tips-query.dto';
import { RecommendationsQueryDto } from './dto/recommendations-query.dto';
import { NearbyGuidesQueryDto } from './dto/nearby-guides-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class HomeController {
    private readonly homeService;
    constructor(homeService: HomeService);
    getHomeData(user: any, query: HomeQueryDto, ctx: RequestContext): Promise<{
        banners: {
            id: string;
            imageUrl: string;
            title: string;
            subtitle: string | null;
            link: string | null;
            priority: number;
        }[] | {
            id: string;
            imageUrl: string;
            title: string;
            link: string;
            priority: number;
        }[];
        categories: {
            id: string;
            name: string;
            icon: string;
            link: string;
        }[];
        gridButtons: {
            id: string;
            title: string;
            icon: string | null;
            type: string | null;
            action: string | null;
            position: string | null;
            color: string | null;
        }[] | {
            id: string;
            title: string;
            icon: string;
            type: string;
            action: string;
            position: number;
            color: string;
        }[];
        recommendedPackages: {
            id: string;
            name: string;
            description: string;
            price: number;
            currency: string;
            imageUrl: string;
            dataSize: number;
            planType: string;
            countries: string[];
        }[];
        travelTips: {
            id: string;
            title: string;
            content: string | null;
            imageUrl: string;
            link: string | null;
            category: string;
        }[] | {
            id: string;
            title: string;
            imageUrl: string;
            link: string;
        }[];
        context?: undefined;
    } | {
        banners: never[];
        categories: never[];
        travelTips: never[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        gridButtons?: undefined;
        recommendedPackages?: undefined;
    }>;
    getBanners(user: any, query: BannerQueryDto, ctx: RequestContext): Promise<{
        banners: {
            id: string;
            type: string;
            title: string;
            subtitle: string | null;
            imageUrl: string;
            link: string | null;
            position: string;
            priority: number;
            startDate: string;
            endDate: string;
        }[];
    }>;
    getTravelTips(user: any, query: TravelTipsQueryDto, ctx: RequestContext): Promise<{
        travelTips: {
            id: any;
            title: any;
            summary: any;
            category: string;
            imageUrl: any;
            content: any;
            link: string;
            readCount: number;
            publishDate: any;
            tags: string[];
        }[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
        };
        filters: {
            category: string | undefined;
            country: string | undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getRecommendations(user: any, query: RecommendationsQueryDto, ctx: RequestContext): Promise<{
        recommendations: ({
            id: string;
            type: string;
            bannerImageUrl: string;
            textLine1: string;
            textLine2: string;
            textLine3: string;
            htmlLink: string;
            priority: number;
            price?: undefined;
            originalPrice?: undefined;
            currency?: undefined;
        } | {
            id: string;
            type: string;
            bannerImageUrl: string;
            textLine1: string;
            textLine2: string;
            textLine3: string;
            htmlLink: string;
            price: number;
            originalPrice: number;
            currency: string;
            priority: number;
        })[];
    }>;
    getNearbyGuides(query: NearbyGuidesQueryDto, ctx: RequestContext): Promise<{
        guides: {
            id: any;
            title: any;
            summary: any;
            imageUrl: any;
            content: any;
            distance: number;
            author: {
                id: string;
                name: string;
                avatar: string;
            };
            publishDate: any;
            readCount: number;
            likeCount: number;
            tags: string[];
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
}
