export declare const LANGUAGES: {
    readonly ZH_CN: "zh-CN";
    readonly EN_US: "en-US";
};
export declare const THEMES: {
    readonly LIGHT: "light";
    readonly DARK: "dark";
};
export declare const CURRENCIES: {
    readonly CNY: "CNY";
    readonly USD: "USD";
};
export declare const DEFAULT_PAGINATION: {
    readonly PAGE: 1;
    readonly PAGE_SIZE: 10;
    readonly MAX_PAGE_SIZE: 50;
};
export declare const DEFAULT_SORT: {
    readonly BY: "price";
    readonly ORDER: "asc";
};
export declare const PACKAGE_TYPES: {
    readonly MONTHLY: "monthly";
    readonly YEARLY: "yearly";
    readonly UNLIMITED: "unlimited";
    readonly DAILY: "daily";
    readonly WEEKLY: "weekly";
};
export declare const ACCOUNT_TYPES: {
    readonly PREPAID: "prepaid";
    readonly POSTPAID: "postpaid";
};
export declare const OPERATORS: {
    readonly CHINA_MOBILE: "china-mobile";
    readonly CHINA_UNICOM: "china-unicom";
    readonly CHINA_TELECOM: "china-telecom";
};
export declare const CONTINENTS: {
    readonly ASIA: "asia";
    readonly EUROPE: "europe";
    readonly AMERICA: "america";
    readonly OCEANIA: "oceania";
    readonly AFRICA: "africa";
};
export declare const REGIONS: {
    readonly ASIA: "asia";
    readonly EUROPE: "europe";
    readonly AMERICA: "america";
    readonly OCEANIA: "oceania";
    readonly AFRICA: "africa";
};
export declare const CONTINENT_COUNTRIES: {
    readonly asia: readonly [{
        readonly code: "JP";
        readonly name: "日本";
        readonly nameEn: "Japan";
    }, {
        readonly code: "KR";
        readonly name: "韩国";
        readonly nameEn: "South Korea";
    }, {
        readonly code: "TH";
        readonly name: "泰国";
        readonly nameEn: "Thailand";
    }, {
        readonly code: "SG";
        readonly name: "新加坡";
        readonly nameEn: "Singapore";
    }, {
        readonly code: "MY";
        readonly name: "马来西亚";
        readonly nameEn: "Malaysia";
    }, {
        readonly code: "VN";
        readonly name: "越南";
        readonly nameEn: "Vietnam";
    }, {
        readonly code: "CN";
        readonly name: "中国";
        readonly nameEn: "China";
    }, {
        readonly code: "IN";
        readonly name: "印度";
        readonly nameEn: "India";
    }, {
        readonly code: "ID";
        readonly name: "印度尼西亚";
        readonly nameEn: "Indonesia";
    }, {
        readonly code: "PH";
        readonly name: "菲律宾";
        readonly nameEn: "Philippines";
    }];
    readonly europe: readonly [{
        readonly code: "GB";
        readonly name: "英国";
        readonly nameEn: "United Kingdom";
    }, {
        readonly code: "FR";
        readonly name: "法国";
        readonly nameEn: "France";
    }, {
        readonly code: "DE";
        readonly name: "德国";
        readonly nameEn: "Germany";
    }, {
        readonly code: "IT";
        readonly name: "意大利";
        readonly nameEn: "Italy";
    }, {
        readonly code: "ES";
        readonly name: "西班牙";
        readonly nameEn: "Spain";
    }, {
        readonly code: "NL";
        readonly name: "荷兰";
        readonly nameEn: "Netherlands";
    }, {
        readonly code: "CH";
        readonly name: "瑞士";
        readonly nameEn: "Switzerland";
    }, {
        readonly code: "AT";
        readonly name: "奥地利";
        readonly nameEn: "Austria";
    }, {
        readonly code: "BE";
        readonly name: "比利时";
        readonly nameEn: "Belgium";
    }, {
        readonly code: "SE";
        readonly name: "瑞典";
        readonly nameEn: "Sweden";
    }];
    readonly america: readonly [{
        readonly code: "US";
        readonly name: "美国";
        readonly nameEn: "United States";
    }, {
        readonly code: "CA";
        readonly name: "加拿大";
        readonly nameEn: "Canada";
    }, {
        readonly code: "MX";
        readonly name: "墨西哥";
        readonly nameEn: "Mexico";
    }, {
        readonly code: "BR";
        readonly name: "巴西";
        readonly nameEn: "Brazil";
    }, {
        readonly code: "AR";
        readonly name: "阿根廷";
        readonly nameEn: "Argentina";
    }, {
        readonly code: "CL";
        readonly name: "智利";
        readonly nameEn: "Chile";
    }, {
        readonly code: "CO";
        readonly name: "哥伦比亚";
        readonly nameEn: "Colombia";
    }, {
        readonly code: "PE";
        readonly name: "秘鲁";
        readonly nameEn: "Peru";
    }];
    readonly oceania: readonly [{
        readonly code: "AU";
        readonly name: "澳大利亚";
        readonly nameEn: "Australia";
    }, {
        readonly code: "NZ";
        readonly name: "新西兰";
        readonly nameEn: "New Zealand";
    }, {
        readonly code: "FJ";
        readonly name: "斐济";
        readonly nameEn: "Fiji";
    }, {
        readonly code: "PG";
        readonly name: "巴布亚新几内亚";
        readonly nameEn: "Papua New Guinea";
    }];
    readonly africa: readonly [{
        readonly code: "ZA";
        readonly name: "南非";
        readonly nameEn: "South Africa";
    }, {
        readonly code: "EG";
        readonly name: "埃及";
        readonly nameEn: "Egypt";
    }, {
        readonly code: "MA";
        readonly name: "摩洛哥";
        readonly nameEn: "Morocco";
    }, {
        readonly code: "KE";
        readonly name: "肯尼亚";
        readonly nameEn: "Kenya";
    }, {
        readonly code: "NG";
        readonly name: "尼日利亚";
        readonly nameEn: "Nigeria";
    }, {
        readonly code: "GH";
        readonly name: "加纳";
        readonly nameEn: "Ghana";
    }];
};
export declare const CONTINENT_NAMES: {
    readonly asia: {
        readonly zh: "亚洲";
        readonly en: "Asia";
    };
    readonly europe: {
        readonly zh: "欧洲";
        readonly en: "Europe";
    };
    readonly america: {
        readonly zh: "美洲";
        readonly en: "America";
    };
    readonly oceania: {
        readonly zh: "大洋洲";
        readonly en: "Oceania";
    };
    readonly africa: {
        readonly zh: "非洲";
        readonly en: "Africa";
    };
};
export declare const DATA_SIZES: {
    readonly MB_100: "100MB";
    readonly MB_500: "500MB";
    readonly GB_1: "1GB";
    readonly GB_3: "3GB";
    readonly GB_5: "5GB";
    readonly GB_10: "10GB";
    readonly GB_20: "20GB";
    readonly UNLIMITED: "unlimited";
};
export declare const BOOSTER_TYPES: {
    readonly EMERGENCY: "emergency";
    readonly DAILY: "daily";
    readonly WEEKLY: "weekly";
    readonly MONTHLY: "monthly";
};
export declare const ACTIVATION_TYPES: {
    readonly INSTANT: "instant";
    readonly SCHEDULED: "scheduled";
};
export declare const ORDER_STATUS: {
    readonly PENDING: "pending";
    readonly COMPLETED: "completed";
    readonly FAILED: "failed";
    readonly CANCELLED: "cancelled";
};
export declare const NETWORK_TYPES: {
    readonly FOUR_G: "4G";
    readonly FIVE_G: "5G";
    readonly FOUR_G_FIVE_G: "4G/5G";
};
export declare const GRID_COLORS: {
    readonly BLUE: "#007AFF";
    readonly GREEN: "#34C759";
    readonly ORANGE: "#FF9500";
    readonly PURPLE: "#5856D6";
    readonly PINK: "#FF2D92";
    readonly RED: "#FF3B30";
    readonly MINT: "#30D158";
    readonly CYAN: "#64D2FF";
    readonly GRAY: "#8E8E93";
};
export declare const CARD_STATUS: {
    readonly PENDING_ACTIVATION: "pending_activation";
    readonly ACTIVE: "active";
    readonly INACTIVE: "inactive";
    readonly EXPIRED: "expired";
};
export declare const CARD_STATUS_NAMES: {
    readonly active: {
        readonly zh: "已激活";
        readonly en: "Active";
    };
    readonly inactive: {
        readonly zh: "未激活";
        readonly en: "Inactive";
    };
    readonly expired: {
        readonly zh: "已过期";
        readonly en: "Expired";
    };
};
