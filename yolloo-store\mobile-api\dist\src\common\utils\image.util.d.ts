export declare class ImageUtil {
    private static readonly BASE_PATH;
    static getBannerUrl(type: 'home' | 'category' | 'promotion' | 'articles' | 'tips', id: string, language?: string, format?: string): string;
    static getIconUrl(name: string, format?: string, size?: number): string;
    static getProductImageUrl(productId: string, imageType?: 'main' | 'thumb' | 'gallery', index?: number, format?: string): string;
    static getAvatarUrl(userId: string, size?: number, format?: string): string;
    static getCountryFlagUrl(countryCode: string, format?: string): string;
    static getPlaceholderUrl(width: number, height: number, text?: string): string;
    static isLocalStaticImage(url: string): boolean;
    static getFullImageUrl(path: string, baseUrl?: string): string;
    static getResponsiveImageUrl(baseName: string, type: 'banner' | 'icon' | 'product', language?: string, deviceType?: 'mobile' | 'tablet' | 'desktop', format?: string): string;
}
export declare const IMAGE_SIZES: {
    readonly BANNER: {
        readonly HOME: {
            readonly width: 750;
            readonly height: 300;
        };
        readonly CATEGORY: {
            readonly width: 750;
            readonly height: 200;
        };
        readonly PROMOTION: {
            readonly width: 750;
            readonly height: 300;
        };
    };
    readonly ICON: {
        readonly SMALL: 24;
        readonly MEDIUM: 48;
        readonly LARGE: 64;
        readonly XLARGE: 128;
    };
    readonly AVATAR: {
        readonly SMALL: 32;
        readonly MEDIUM: 64;
        readonly LARGE: 128;
        readonly XLARGE: 256;
    };
    readonly PRODUCT: {
        readonly THUMB: {
            readonly width: 150;
            readonly height: 150;
        };
        readonly MAIN: {
            readonly width: 400;
            readonly height: 400;
        };
        readonly GALLERY: {
            readonly width: 800;
            readonly height: 600;
        };
    };
};
export declare const SUPPORTED_FORMATS: readonly ["webp", "jpg", "jpeg", "png", "svg"];
export type SupportedFormat = typeof SUPPORTED_FORMATS[number];
