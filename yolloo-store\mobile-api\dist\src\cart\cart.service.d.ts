import { PrismaService } from '../prisma.service';
import { AddCartItemDto } from './dto/add-cart-item.dto';
import { UpdateCartItemDto } from './dto/update-cart-item.dto';
export declare class CartService {
    private prisma;
    constructor(prisma: PrismaService);
    getCart(userId: string): Promise<{
        items: {
            id: string;
            productId: string;
            variantId: string | null;
            name: string;
            price: number;
            currency: string;
            quantity: number;
            imageUrl: string;
            attributes: {
                dataSize: number;
                duration: number;
                countries: any;
            };
        }[];
        summary: {
            subtotal: number;
            shipping: number;
            tax: number;
            total: number;
            currency: string;
        };
    }>;
    addCartItem(userId: string, addCartItemDto: AddCartItemDto): Promise<{
        id: any;
        productId: any;
        variantId: any;
        quantity: any;
        message: string;
    }>;
    updateCartItem(userId: string, itemId: string, updateCartItemDto: UpdateCartItemDto): Promise<{
        id: string;
        quantity: number;
        message: string;
    }>;
    removeCartItem(userId: string, itemId: string): Promise<{
        message: string;
    }>;
}
