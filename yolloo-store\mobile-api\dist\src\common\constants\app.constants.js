"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CARD_STATUS_NAMES = exports.CARD_STATUS = exports.GRID_COLORS = exports.NETWORK_TYPES = exports.ORDER_STATUS = exports.ACTIVATION_TYPES = exports.BOOSTER_TYPES = exports.DATA_SIZES = exports.CONTINENT_NAMES = exports.CONTINENT_COUNTRIES = exports.REGIONS = exports.CONTINENTS = exports.OPERATORS = exports.ACCOUNT_TYPES = exports.PACKAGE_TYPES = exports.DEFAULT_SORT = exports.DEFAULT_PAGINATION = exports.CURRENCIES = exports.THEMES = exports.LANGUAGES = void 0;
exports.LANGUAGES = {
    ZH_CN: 'zh-CN',
    EN_US: 'en-US',
};
exports.THEMES = {
    LIGHT: 'light',
    DARK: 'dark',
};
exports.CURRENCIES = {
    CNY: 'CNY',
    USD: 'USD',
};
exports.DEFAULT_PAGINATION = {
    PAGE: 1,
    PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 50,
};
exports.DEFAULT_SORT = {
    BY: 'price',
    ORDER: 'asc',
};
exports.PACKAGE_TYPES = {
    MONTHLY: 'monthly',
    YEARLY: 'yearly',
    UNLIMITED: 'unlimited',
    DAILY: 'daily',
    WEEKLY: 'weekly',
};
exports.ACCOUNT_TYPES = {
    PREPAID: 'prepaid',
    POSTPAID: 'postpaid',
};
exports.OPERATORS = {
    CHINA_MOBILE: 'china-mobile',
    CHINA_UNICOM: 'china-unicom',
    CHINA_TELECOM: 'china-telecom',
};
exports.CONTINENTS = {
    ASIA: 'asia',
    EUROPE: 'europe',
    AMERICA: 'america',
    OCEANIA: 'oceania',
    AFRICA: 'africa',
};
exports.REGIONS = exports.CONTINENTS;
exports.CONTINENT_COUNTRIES = {
    [exports.CONTINENTS.ASIA]: [
        { code: 'JP', name: '日本', nameEn: 'Japan' },
        { code: 'KR', name: '韩国', nameEn: 'South Korea' },
        { code: 'TH', name: '泰国', nameEn: 'Thailand' },
        { code: 'SG', name: '新加坡', nameEn: 'Singapore' },
        { code: 'MY', name: '马来西亚', nameEn: 'Malaysia' },
        { code: 'VN', name: '越南', nameEn: 'Vietnam' },
        { code: 'CN', name: '中国', nameEn: 'China' },
        { code: 'IN', name: '印度', nameEn: 'India' },
        { code: 'ID', name: '印度尼西亚', nameEn: 'Indonesia' },
        { code: 'PH', name: '菲律宾', nameEn: 'Philippines' },
    ],
    [exports.CONTINENTS.EUROPE]: [
        { code: 'GB', name: '英国', nameEn: 'United Kingdom' },
        { code: 'FR', name: '法国', nameEn: 'France' },
        { code: 'DE', name: '德国', nameEn: 'Germany' },
        { code: 'IT', name: '意大利', nameEn: 'Italy' },
        { code: 'ES', name: '西班牙', nameEn: 'Spain' },
        { code: 'NL', name: '荷兰', nameEn: 'Netherlands' },
        { code: 'CH', name: '瑞士', nameEn: 'Switzerland' },
        { code: 'AT', name: '奥地利', nameEn: 'Austria' },
        { code: 'BE', name: '比利时', nameEn: 'Belgium' },
        { code: 'SE', name: '瑞典', nameEn: 'Sweden' },
    ],
    [exports.CONTINENTS.AMERICA]: [
        { code: 'US', name: '美国', nameEn: 'United States' },
        { code: 'CA', name: '加拿大', nameEn: 'Canada' },
        { code: 'MX', name: '墨西哥', nameEn: 'Mexico' },
        { code: 'BR', name: '巴西', nameEn: 'Brazil' },
        { code: 'AR', name: '阿根廷', nameEn: 'Argentina' },
        { code: 'CL', name: '智利', nameEn: 'Chile' },
        { code: 'CO', name: '哥伦比亚', nameEn: 'Colombia' },
        { code: 'PE', name: '秘鲁', nameEn: 'Peru' },
    ],
    [exports.CONTINENTS.OCEANIA]: [
        { code: 'AU', name: '澳大利亚', nameEn: 'Australia' },
        { code: 'NZ', name: '新西兰', nameEn: 'New Zealand' },
        { code: 'FJ', name: '斐济', nameEn: 'Fiji' },
        { code: 'PG', name: '巴布亚新几内亚', nameEn: 'Papua New Guinea' },
    ],
    [exports.CONTINENTS.AFRICA]: [
        { code: 'ZA', name: '南非', nameEn: 'South Africa' },
        { code: 'EG', name: '埃及', nameEn: 'Egypt' },
        { code: 'MA', name: '摩洛哥', nameEn: 'Morocco' },
        { code: 'KE', name: '肯尼亚', nameEn: 'Kenya' },
        { code: 'NG', name: '尼日利亚', nameEn: 'Nigeria' },
        { code: 'GH', name: '加纳', nameEn: 'Ghana' },
    ],
};
exports.CONTINENT_NAMES = {
    [exports.CONTINENTS.ASIA]: { zh: '亚洲', en: 'Asia' },
    [exports.CONTINENTS.EUROPE]: { zh: '欧洲', en: 'Europe' },
    [exports.CONTINENTS.AMERICA]: { zh: '美洲', en: 'America' },
    [exports.CONTINENTS.OCEANIA]: { zh: '大洋洲', en: 'Oceania' },
    [exports.CONTINENTS.AFRICA]: { zh: '非洲', en: 'Africa' },
};
exports.DATA_SIZES = {
    MB_100: '100MB',
    MB_500: '500MB',
    GB_1: '1GB',
    GB_3: '3GB',
    GB_5: '5GB',
    GB_10: '10GB',
    GB_20: '20GB',
    UNLIMITED: 'unlimited',
};
exports.BOOSTER_TYPES = {
    EMERGENCY: 'emergency',
    DAILY: 'daily',
    WEEKLY: 'weekly',
    MONTHLY: 'monthly',
};
exports.ACTIVATION_TYPES = {
    INSTANT: 'instant',
    SCHEDULED: 'scheduled',
};
exports.ORDER_STATUS = {
    PENDING: 'pending',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled',
};
exports.NETWORK_TYPES = {
    FOUR_G: '4G',
    FIVE_G: '5G',
    FOUR_G_FIVE_G: '4G/5G',
};
exports.GRID_COLORS = {
    BLUE: '#007AFF',
    GREEN: '#34C759',
    ORANGE: '#FF9500',
    PURPLE: '#5856D6',
    PINK: '#FF2D92',
    RED: '#FF3B30',
    MINT: '#30D158',
    CYAN: '#64D2FF',
    GRAY: '#8E8E93',
};
exports.CARD_STATUS = {
    PENDING_ACTIVATION: 'pending_activation',
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    EXPIRED: 'expired',
};
exports.CARD_STATUS_NAMES = {
    [exports.CARD_STATUS.ACTIVE]: {
        zh: '已激活',
        en: 'Active',
    },
    [exports.CARD_STATUS.INACTIVE]: {
        zh: '未激活',
        en: 'Inactive',
    },
    [exports.CARD_STATUS.EXPIRED]: {
        zh: '已过期',
        en: 'Expired',
    },
};
//# sourceMappingURL=app.constants.js.map