"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PagesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PagesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let PagesService = PagesService_1 = class PagesService {
    prisma;
    logger = new common_1.Logger(PagesService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getPageConfigs(query, ctx) {
        console.log('Context in getPageConfigs:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const categories = await this.prisma.category.findMany({
                where: {
                    OR: [
                        { name: { contains: 'page', mode: 'insensitive' } },
                        { name: { contains: '页面', mode: 'insensitive' } },
                        { description: { contains: 'page', mode: 'insensitive' } },
                        { description: { contains: '页面', mode: 'insensitive' } },
                    ],
                },
                include: {
                    products: {
                        where: {
                            status: 'ACTIVE',
                            off_shelve: false,
                        },
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            websiteDescription: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: 'asc',
                },
            });
            if (categories.length === 0) {
                this.logger.warn('No page configurations found in database, using fallback data');
                return this.getFallbackPageConfigs(query, ctx);
            }
            const pageConfigs = categories.map((category, index) => {
                const pageId = this.generatePageId(category.name);
                const categoryType = this.determineCategoryType(category.name, category.description || undefined);
                return {
                    id: pageId,
                    title: category.name,
                    description: category.description || (isZh ? '页面配置' : 'Page configuration'),
                    icon: this.getIconForCategory(categoryType),
                    type: 'html',
                    category: categoryType,
                    position: index + 1,
                    isActive: true,
                    requiresAuth: false,
                    url: `/pages/${pageId}`,
                    metadata: {
                        lastUpdated: category.updatedAt.toISOString(),
                        version: '1.0.0',
                        cacheTime: 3600,
                        productCount: category.products.length,
                    },
                };
            });
            this.logger.log(`Found ${pageConfigs.length} page configurations`);
            let filteredConfigs = pageConfigs;
            if (query.category) {
                filteredConfigs = pageConfigs.filter(config => config.category === query.category);
            }
            return {
                pages: filteredConfigs,
                total: filteredConfigs.length,
                layout: query.layout || 'grid',
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching page configurations:', error);
            return this.getFallbackPageConfigs(query, ctx);
        }
    }
    getFallbackPageConfigs(query, ctx) {
        const isZh = ctx.language.startsWith('zh');
        const pageConfigs = [
            {
                id: 'data-cards',
                title: ctx.language.startsWith('zh') ? '流量卡' : 'Data Cards',
                description: ctx.language.startsWith('zh') ? '各种流量卡套餐选择' : 'Various data card package options',
                icon: 'credit-card',
                type: 'html',
                category: 'products',
                position: 1,
                isActive: true,
                requiresAuth: false,
                url: '/pages/data-cards',
                metadata: {
                    lastUpdated: '2023-12-01T00:00:00Z',
                    version: '1.0.0',
                    cacheTime: 3600,
                },
            },
            {
                id: 'travel-data',
                title: ctx.language.startsWith('zh') ? '出行流量' : 'Travel Data',
                description: ctx.language.startsWith('zh') ? '专为旅行设计的流量套餐' : 'Data packages designed for travel',
                icon: 'map-pin',
                type: 'html',
                category: 'travel',
                position: 2,
                isActive: true,
                requiresAuth: false,
                url: '/pages/travel-data',
                metadata: {
                    lastUpdated: '2023-12-01T00:00:00Z',
                    version: '1.0.0',
                    cacheTime: 7200,
                },
            },
            {
                id: '5g-packages',
                title: ctx.language.startsWith('zh') ? '5G套餐' : '5G Packages',
                description: ctx.language.startsWith('zh') ? '高速5G网络套餐' : 'High-speed 5G network packages',
                icon: 'zap',
                type: 'html',
                category: 'products',
                position: 3,
                isActive: true,
                requiresAuth: false,
                url: '/pages/5g-packages',
                metadata: {
                    lastUpdated: '2023-12-01T00:00:00Z',
                    version: '1.0.0',
                    cacheTime: 1800,
                },
            },
            {
                id: 'international-roaming',
                title: ctx.language.startsWith('zh') ? '国际漫游' : 'International Roaming',
                description: ctx.language.startsWith('zh') ? '全球漫游服务和套餐' : 'Global roaming services and packages',
                icon: 'globe',
                type: 'html',
                category: 'services',
                position: 4,
                isActive: true,
                requiresAuth: false,
                url: '/pages/international-roaming',
                metadata: {
                    lastUpdated: '2023-12-01T00:00:00Z',
                    version: '1.0.0',
                    cacheTime: 86400,
                },
            },
        ];
        let filteredConfigs = pageConfigs;
        if (query.category) {
            filteredConfigs = pageConfigs.filter(config => config.category === query.category);
        }
        return {
            pages: filteredConfigs,
            total: filteredConfigs.length,
            layout: query.layout || 'grid',
            context: {
                language: ctx.language,
                theme: ctx.theme,
                currency: ctx.currency,
            },
        };
    }
    async getPageContent(query, ctx) {
        console.log('Context in getPageContent:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const page = await this.prisma.page.findFirst({
                where: {
                    pageId: query.pageId,
                    language: isZh ? 'zh' : 'en',
                    isPublished: true,
                },
            });
            if (page) {
                this.logger.log(`Found page content for: ${query.pageId} in ${page.language}`);
                return {
                    pageId: query.pageId,
                    title: page.title,
                    content: page.content,
                    styles: page.styles || this.generatePageStyles(query.pageId),
                    metadata: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                        lastUpdated: page.updatedAt.toISOString(),
                        version: query.version || '1.0.0',
                        sourceType: 'database',
                    },
                    assets: query.includeAssets ? {
                        scripts: [],
                        stylesheets: [],
                        images: [],
                    } : undefined,
                };
            }
            const categoryName = this.getCategoryNameFromPageId(query.pageId);
            const category = await this.prisma.category.findFirst({
                where: {
                    OR: [
                        { name: { contains: categoryName, mode: 'insensitive' } },
                        { description: { contains: categoryName, mode: 'insensitive' } },
                    ],
                },
                include: {
                    products: {
                        where: {
                            status: 'ACTIVE',
                            off_shelve: false,
                            websiteDescription: { not: '' },
                        },
                        select: {
                            id: true,
                            name: true,
                            description: true,
                            websiteDescription: true,
                            images: true,
                            updatedAt: true,
                        },
                        orderBy: {
                            createdAt: 'desc',
                        },
                        take: 1,
                    },
                },
            });
            if (category && category.products.length > 0) {
                const product = category.products[0];
                const content = this.generatePageContent(query.pageId, product, isZh);
                try {
                    await this.prisma.page.create({
                        data: {
                            pageId: query.pageId,
                            title: content.title,
                            content: content.html,
                            styles: content.styles,
                            language: isZh ? 'zh' : 'en',
                            isPublished: true,
                        },
                    });
                    this.logger.log(`Created page record for: ${query.pageId}`);
                }
                catch (createError) {
                    this.logger.warn(`Failed to create page record: ${createError.message}`);
                }
                this.logger.log(`Generated content for page: ${query.pageId} from product`);
                return {
                    pageId: query.pageId,
                    title: content.title,
                    content: content.html,
                    styles: content.styles,
                    metadata: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                        lastUpdated: product.updatedAt?.toISOString() || new Date().toISOString(),
                        version: query.version || '1.0.0',
                        sourceProductId: product.id,
                        sourceType: 'generated',
                    },
                    assets: query.includeAssets ? {
                        scripts: [],
                        stylesheets: [],
                        images: product.images || [],
                    } : undefined,
                };
            }
            this.logger.warn(`No content found for page: ${query.pageId}, using fallback`);
            return this.getFallbackPageContent(query, ctx);
        }
        catch (error) {
            this.logger.error('Error fetching page content:', error);
            return this.getFallbackPageContent(query, ctx);
        }
    }
    getFallbackPageContent(query, ctx) {
        const isZh = ctx.language.startsWith('zh');
        const contentMap = {
            'data-cards': {
                'zh': {
                    title: '流量卡',
                    content: `<div class="data-cards"><h1>流量卡套餐</h1><p>选择适合您需求的流量卡套餐，享受高速稳定的网络连接。</p></div>`,
                    styles: `.data-cards { padding: 20px; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }`,
                },
                'en': {
                    title: 'Data Cards',
                    content: `<div class="data-cards"><h1>Data Card Packages</h1><p>Choose the data card package that suits your needs.</p></div>`,
                    styles: `.data-cards { padding: 20px; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }`,
                },
            },
            'travel-data': {
                'zh': {
                    title: '出行流量',
                    content: `<div class="travel-data"><h1>出行流量套餐</h1><p>专为旅行者设计的流量套餐，让您的旅程更加便捷。</p></div>`,
                    styles: `.travel-data { padding: 20px; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }`,
                },
                'en': {
                    title: 'Travel Data',
                    content: `<div class="travel-data"><h1>Travel Data Packages</h1><p>Data packages designed specifically for travelers.</p></div>`,
                    styles: `.travel-data { padding: 20px; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }`,
                },
            },
            '5g-packages': {
                'zh': {
                    title: '5G套餐',
                    content: `<div class="5g-packages"><h1>5G高速套餐</h1><p>体验极速5G网络，享受超快的上网体验。</p></div>`,
                    styles: `.5g-packages { padding: 20px; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }`,
                },
                'en': {
                    title: '5G Packages',
                    content: `<div class="5g-packages"><h1>5G High-Speed Packages</h1><p>Experience ultra-fast 5G network speeds.</p></div>`,
                    styles: `.5g-packages { padding: 20px; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }`,
                },
            },
            'international-roaming': {
                'zh': {
                    title: '国际漫游',
                    content: `<div class="international-roaming"><h1>国际漫游服务</h1><p>全球200+国家和地区的漫游服务，让您畅游世界。</p></div>`,
                    styles: `.international-roaming { padding: 20px; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }`,
                },
                'en': {
                    title: 'International Roaming',
                    content: `<div class="international-roaming"><h1>International Roaming Services</h1><p>Roaming services in 200+ countries and regions worldwide.</p></div>`,
                    styles: `.international-roaming { padding: 20px; font-family: -apple-system, BlinkMacSystemFont, sans-serif; }`,
                },
            },
        };
        const language = ctx.language.startsWith('zh') ? 'zh' : 'en';
        const pageContent = contentMap[query.pageId]?.[language];
        if (!pageContent) {
            return {
                error: 'Page not found',
                pageId: query.pageId,
                availablePages: Object.keys(contentMap),
            };
        }
        return {
            pageId: query.pageId,
            title: pageContent.title,
            content: pageContent.content,
            styles: pageContent.styles,
            metadata: {
                language: ctx.language,
                theme: ctx.theme,
                currency: ctx.currency,
                lastUpdated: '2023-12-01T00:00:00Z',
                version: query.version || '1.0.0',
            },
            assets: query.includeAssets ? {
                scripts: [],
                stylesheets: [],
                images: [],
            } : undefined,
        };
    }
    generatePageId(categoryName) {
        return categoryName
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-')
            .substring(0, 50);
    }
    getCategoryNameFromPageId(pageId) {
        const nameMap = {
            'data-cards': '流量卡',
            'travel-data': '出行流量',
            '5g-packages': '5G套餐',
            'international-roaming': '国际漫游',
        };
        return nameMap[pageId] || pageId.replace(/-/g, ' ');
    }
    determineCategoryType(name, description) {
        const text = (name + ' ' + (description || '')).toLowerCase();
        if (text.includes('travel') || text.includes('旅行') || text.includes('出行'))
            return 'travel';
        if (text.includes('5g'))
            return 'products';
        if (text.includes('roaming') || text.includes('漫游'))
            return 'services';
        if (text.includes('card') || text.includes('卡'))
            return 'products';
        return 'products';
    }
    getIconForCategory(categoryType) {
        const iconMap = {
            'products': 'credit-card',
            'travel': 'map-pin',
            'services': 'globe',
        };
        return iconMap[categoryType] || 'grid';
    }
    generatePageContent(pageId, product, isZh) {
        const title = product.name;
        const description = product.description;
        const websiteDescription = product.websiteDescription;
        let htmlContent = '';
        if (websiteDescription && websiteDescription.trim() !== '') {
            if (websiteDescription.includes('<')) {
                htmlContent = websiteDescription;
            }
            else {
                htmlContent = this.convertTextToHtml(websiteDescription, title, isZh);
            }
        }
        else {
            htmlContent = this.generateProductBasedContent(product, pageId, isZh);
        }
        const styles = this.generatePageStyles(pageId);
        return {
            title: title,
            html: htmlContent,
            styles: styles,
        };
    }
    convertTextToHtml(text, title, isZh) {
        const paragraphs = text.split('\n\n').filter(p => p.trim() !== '');
        let html = `<div class="page-content">
      <h1>${title}</h1>`;
        paragraphs.forEach(paragraph => {
            if (paragraph.trim().startsWith('#')) {
                const level = paragraph.match(/^#+/)?.[0].length || 1;
                const titleText = paragraph.replace(/^#+\s*/, '');
                html += `<h${Math.min(level + 1, 6)}>${titleText}</h${Math.min(level + 1, 6)}>`;
            }
            else {
                html += `<p>${paragraph.trim()}</p>`;
            }
        });
        html += '</div>';
        return html;
    }
    generateProductBasedContent(product, pageId, isZh) {
        const title = product.name;
        const description = product.description;
        return `<div class="page-content ${pageId}">
      <h1>${title}</h1>
      <p class="description">${description}</p>

      <div class="features">
        <h2>${isZh ? '产品特点' : 'Product Features'}</h2>
        <ul>
          <li>${isZh ? '高质量的服务体验' : 'High-quality service experience'}</li>
          <li>${isZh ? '简单易用的操作界面' : 'Simple and easy-to-use interface'}</li>
          <li>${isZh ? '全天候的客户支持' : '24/7 customer support'}</li>
          <li>${isZh ? '安全可靠的技术保障' : 'Secure and reliable technology'}</li>
        </ul>
      </div>

      <div class="cta">
        <h2>${isZh ? '立即开始' : 'Get Started'}</h2>
        <p>${isZh ? '选择适合您需求的套餐，享受优质服务。' : 'Choose the package that suits your needs and enjoy quality service.'}</p>
        <button class="cta-button">${isZh ? '了解更多' : 'Learn More'}</button>
      </div>
    </div>`;
    }
    generatePageStyles(pageId) {
        return `
      .page-content {
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
      }

      .page-content h1 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 2em;
      }

      .page-content h2 {
        color: #34495e;
        margin-top: 30px;
        margin-bottom: 15px;
        font-size: 1.5em;
      }

      .page-content p {
        margin-bottom: 15px;
        color: #555;
      }

      .page-content .description {
        font-size: 1.1em;
        color: #666;
        margin-bottom: 30px;
      }

      .page-content ul {
        margin-bottom: 20px;
        padding-left: 20px;
      }

      .page-content li {
        margin-bottom: 8px;
      }

      .cta {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-top: 30px;
        text-align: center;
      }

      .cta-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-size: 16px;
        cursor: pointer;
        margin-top: 15px;
      }

      .cta-button:hover {
        background: #0056b3;
      }

      @media (max-width: 768px) {
        .page-content {
          padding: 15px;
        }

        .page-content h1 {
          font-size: 1.5em;
        }

        .page-content h2 {
          font-size: 1.3em;
        }
      }
    `;
    }
};
PagesService = PagesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PagesService);
exports.PagesService = PagesService;
//# sourceMappingURL=pages.service.js.map