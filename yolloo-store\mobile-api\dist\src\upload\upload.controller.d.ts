/// <reference types="multer" />
import { Response } from 'express';
import { UploadService } from './upload.service';
export declare class UploadController {
    private readonly uploadService;
    constructor(uploadService: UploadService);
    uploadImage(file: Express.Multer.File, category: string | undefined, req: any): Promise<{
        id: string;
        fileName: string;
        originalName: string;
        url: string;
        size: number;
        mimeType: string;
        category: "products" | "categories" | "users" | "rewards" | "temp";
        uploadedAt: Date;
    }>;
    uploadMultipleImages(files: Express.Multer.File[], category: string | undefined, req: any): Promise<{
        success: any[];
        errors: any[];
        total: number;
        uploaded: number;
        failed: number;
    }>;
    getUpload(id: string): Promise<import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        filename: string;
        originalName: string;
        mimeType: string;
        size: number;
        url: string;
        createdAt: Date;
    }, unknown> & {}>;
    serveFile(id: string, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    deleteUpload(id: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    getUserUploads(req: any, category?: string): Promise<(import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        filename: string;
        originalName: string;
        mimeType: string;
        size: number;
        url: string;
        createdAt: Date;
    }, unknown> & {})[]>;
    moveUpload(id: string, category: string, req: any): Promise<import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        filename: string;
        originalName: string;
        mimeType: string;
        size: number;
        url: string;
        createdAt: Date;
    }, unknown> & {}>;
    cleanupTempFiles(hours?: string): Promise<{
        deletedCount: number;
    }>;
}
