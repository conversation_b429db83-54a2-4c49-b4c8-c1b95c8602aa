import { MobileRechargeService } from './mobile-recharge.service';
import { RechargeQueryDto, RechargeOrderDto } from './dto/recharge-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class MobileRechargeController {
    private readonly mobileRechargeService;
    constructor(mobileRechargeService: MobileRechargeService);
    getRechargeOptions(query: RechargeQueryDto, ctx: RequestContext): Promise<{
        rechargeOptions: ({
            id: string;
            operator: string;
            operatorName: string;
            amount: number;
            currency: string;
            discount: number;
            finalAmount: number;
            accountType: string;
            description: string;
            processingTime: string;
            imageUrl: string;
            isPopular?: undefined;
        } | {
            id: string;
            operator: string;
            operatorName: string;
            amount: number;
            currency: string;
            discount: number;
            finalAmount: number;
            accountType: string;
            description: string;
            processingTime: string;
            imageUrl: string;
            isPopular: boolean;
        })[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        operators: {
            id: string | null;
            name: string | null;
            logo: string;
            country: string;
        }[] | {
            id: string;
            name: string;
            logo: string;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    createRechargeOrder(orderData: RechargeOrderDto, ctx: RequestContext): Promise<{
        order: {
            id: string;
            phoneNumber: string;
            operator: string;
            amount: number;
            currency: string;
            status: string;
            statusText: string;
            createdAt: string;
            estimatedCompletionTime: string;
            description: string;
        };
        message: string;
    }>;
    getRechargeHistory(userId: string, query: RechargeQueryDto, ctx: RequestContext): Promise<{
        history: {
            id: string;
            phoneNumber: string | null;
            operator: string | null;
            operatorName: string | null;
            amount: number;
            currency: string;
            status: string;
            statusText: string;
            createdAt: string;
            completedAt: string | null;
            failureReason: string | null;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
    } | {
        history: {
            id: string;
            phoneNumber: string;
            operator: string;
            operatorName: string;
            amount: number;
            currency: string;
            status: string;
            statusText: string;
            createdAt: string;
            completedAt: string;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
    }>;
}
