import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { LoginCodeDto } from './dto/login-code.dto';
import { SocialLoginDto } from './dto/social-login.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        id: string;
        email: string | null;
        name: string | null;
        token: string;
    }>;
    login(loginDto: LoginDto): Promise<{
        id: string;
        email: string | null;
        name: string | null;
        image: string | null;
        token: string;
    }>;
    loginWithCode(loginCodeDto: LoginCodeDto): Promise<{
        status: string;
        message: string;
        id?: undefined;
        email?: undefined;
        name?: undefined;
        token?: undefined;
    } | {
        id: string;
        email: string | null;
        name: string | null;
        token: string;
        status?: undefined;
        message?: undefined;
    }>;
    socialLogin(socialLoginDto: SocialLoginDto): Promise<{
        id: any;
        email: any;
        name: any;
        image: any;
        token: string;
    }>;
}
