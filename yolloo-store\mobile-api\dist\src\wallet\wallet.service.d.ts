import { PrismaService } from '../prisma.service';
export declare class WalletService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getOrCreateWallet(userId: string): Promise<{
        transactions: (import("@prisma/client/runtime").GetResult<{
            id: string;
            walletId: string;
            type: import(".prisma/client").TransactionType;
            amount: number;
            currency: string;
            description: string;
            status: import(".prisma/client").TransactionStatus;
            reference: string | null;
            metadata: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
        paymentCards: (import("@prisma/client/runtime").GetResult<{
            id: string;
            walletId: string;
            cardNumber: string;
            cardHolder: string;
            expiryMonth: number;
            expiryYear: number;
            cardType: string;
            type: string | null;
            brand: string | null;
            last4: string | null;
            isDefault: boolean;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        balance: number;
        currency: string;
        createdAt: Date;
        updatedAt: Date;
    }, unknown> & {}>;
    addTransaction(walletId: string, type: 'DEPOSIT' | 'PAYMENT' | 'REFUND' | 'WITHDRAWAL', amount: number, currency: string, description: string, reference?: string, metadata?: any): Promise<import("@prisma/client/runtime").GetResult<{
        id: string;
        walletId: string;
        type: import(".prisma/client").TransactionType;
        amount: number;
        currency: string;
        description: string;
        status: import(".prisma/client").TransactionStatus;
        reference: string | null;
        metadata: import(".prisma/client").Prisma.JsonValue;
        createdAt: Date;
        updatedAt: Date;
    }, unknown> & {}>;
    updateTransactionStatus(transactionId: string, status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'): Promise<{
        wallet: import("@prisma/client/runtime").GetResult<{
            id: string;
            userId: string;
            balance: number;
            currency: string;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {};
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        walletId: string;
        type: import(".prisma/client").TransactionType;
        amount: number;
        currency: string;
        description: string;
        status: import(".prisma/client").TransactionStatus;
        reference: string | null;
        metadata: import(".prisma/client").Prisma.JsonValue;
        createdAt: Date;
        updatedAt: Date;
    }, unknown> & {}>;
    processPayment(userId: string, amount: number, description: string, orderId?: string): Promise<import("@prisma/client/runtime").GetResult<{
        id: string;
        walletId: string;
        type: import(".prisma/client").TransactionType;
        amount: number;
        currency: string;
        description: string;
        status: import(".prisma/client").TransactionStatus;
        reference: string | null;
        metadata: import(".prisma/client").Prisma.JsonValue;
        createdAt: Date;
        updatedAt: Date;
    }, unknown> & {}>;
    addPaymentCard(userId: string, cardData: {
        type: string;
        brand: string;
        last4: string;
        expiryMonth: number;
        expiryYear: number;
        isDefault?: boolean;
    }): Promise<import("@prisma/client/runtime").GetResult<{
        id: string;
        walletId: string;
        cardNumber: string;
        cardHolder: string;
        expiryMonth: number;
        expiryYear: number;
        cardType: string;
        type: string | null;
        brand: string | null;
        last4: string | null;
        isDefault: boolean;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
    }, unknown> & {}>;
    removePaymentCard(userId: string, cardId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    setDefaultPaymentCard(userId: string, cardId: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
