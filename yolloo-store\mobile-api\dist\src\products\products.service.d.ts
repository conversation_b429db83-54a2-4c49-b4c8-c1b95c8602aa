import { PrismaService } from '../prisma.service';
import { ProductSearchDto } from './dto/product-search.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class ProductsService {
    private prisma;
    constructor(prisma: PrismaService);
    getCategories(ctx: RequestContext): Promise<{
        categories: {
            id: string;
            name: string;
            description: string | null;
            imageUrl: string | null;
        }[];
    }>;
    getProductsByCategory(categoryId: string, page: number | undefined, pageSize: number | undefined, ctx: RequestContext): Promise<{
        products: {
            id: string;
            name: string;
            description: string;
            price: number;
            currency: string;
            imageUrl: string | null;
            dataSize: number;
            planType: string;
            duration: number;
            countries: any;
            rating: number;
            reviewCount: number;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
    }>;
    searchProducts(searchDto: ProductSearchDto, ctx: RequestContext): Promise<{
        products: {
            id: string;
            name: string;
            description: string;
            price: number;
            currency: string;
            imageUrl: string | null;
            dataSize: number;
            planType: string;
            duration: number;
            countries: any;
            rating: number;
            reviewCount: number;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
    }>;
    getProductById(productId: string, ctx: RequestContext): Promise<{
        id: string;
        name: string;
        description: string;
        fullDescription: string;
        price: number;
        currency: string;
        images: string[];
        dataSize: number;
        planType: string;
        duration: number;
        countries: any;
        countryCount: any;
        specifications: {
            network: any;
            activation: any;
            provider: any;
        };
        variants: {
            id: string;
            attributes: string | number | true | import(".prisma/client").Prisma.JsonObject | import(".prisma/client").Prisma.JsonArray;
            price: number;
            currency: string;
        }[];
        reviews: {
            id: string;
            rating: number;
            comment: string;
            user: {
                name: string;
                image: string | null;
            };
            date: string;
        }[];
        rating: number;
        reviewCount: number;
        activationInstructions: string;
    }>;
}
