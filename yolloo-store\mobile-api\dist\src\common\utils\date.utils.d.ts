export declare const APP_CONFIG: {
    readonly TIMEZONE: string;
    readonly LOCALE: string;
    readonly DATE_FORMAT: string;
    readonly DATETIME_FORMAT: string;
};
export declare function safeParseDate(input: unknown): Date | null;
export declare const DateFormatter: {
    short: (input: unknown, fallback?: string) => string;
    full: (input: unknown, fallback?: string) => string;
    long: (input: unknown, fallback?: string) => string;
    relative: (input: unknown, fallback?: string) => string;
    time: (input: unknown, fallback?: string) => string;
    timeShort: (input: unknown, fallback?: string) => string;
    withTimezone: (input: unknown, timezone?: string, fallback?: string) => string;
    iso: (input: unknown, fallback?: string) => string;
    custom: (input: unknown, formatString: string, fallback?: string) => string;
};
export declare const DateUtils: {
    addDays: (date: Date | string | number, days: number) => Date;
    addHours: (date: Date | string | number, hours: number) => Date;
    addMinutes: (date: Date | string | number, minutes: number) => Date;
    daysBetween: (date1: Date | string | number, date2: Date | string | number) => number;
    isExpired: (date: Date | string | number) => boolean;
};
export declare function formatDate(input: string | number | Date): string;
export declare function formatDatetime(input: string | number | Date): string;
export declare function formatDateYMDHM(input: string | number | Date): string;
