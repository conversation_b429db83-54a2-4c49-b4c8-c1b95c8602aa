import { UsersService } from './users.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { WalletDepositDto } from './dto/wallet-deposit.dto';
import { NotificationQueryDto } from './dto/notification-query.dto';
import { CheckinHistoryQueryDto } from './dto/checkin-history-query.dto';
import { CouponQueryDto } from './dto/coupon-query.dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    getProfile(user: any): Promise<{
        id: string;
        email: string | null;
        name: string | null;
        image: string | null;
        walletBalance: number;
        currency: string;
        points: number;
        totalSpent: number;
        orderCount: number;
        memberSince: Date;
    }>;
    updateProfile(user: any, updateProfileDto: UpdateProfileDto): Promise<{
        id: string;
        email: string | null;
        name: string | null;
        image: string | null;
    }>;
    getPoints(user: any): Promise<{
        points: number;
        level: string;
        nextLevel: string;
        pointsToNextLevel: number;
        pointsExpiringSoon: number;
        expiryDate: null;
        history: {
            id: string;
            type: "earned";
            amount: number;
            description: string;
            transactionDate: string;
            orderRef: string;
        }[];
        rewards: {
            id: string;
            name: string;
            description: string;
            pointsCost: number;
            imageUrl: string;
        }[];
    }>;
    checkin(user: any): Promise<{
        success: boolean;
        message: string;
        pointsEarned: number;
        alreadyCheckedIn: boolean;
        currentStreak?: undefined;
        totalCheckins?: undefined;
        nextReward?: undefined;
    } | {
        success: boolean;
        message: string;
        pointsEarned: number;
        currentStreak: number;
        totalCheckins: number;
        nextReward: {
            days: number;
            reward: string;
            remaining: number;
        };
        alreadyCheckedIn?: undefined;
    }>;
    getCheckinHistory(user: any, query: CheckinHistoryQueryDto): Promise<{
        currentStreak: number;
        longestStreak: number;
        totalCheckins: number;
        currentMonth: string;
        checkinDays: number[];
        rewards: {
            streakDays: number;
            reward: string;
            claimed: boolean;
            claimedDate: string | null;
        }[];
    }>;
    getNotifications(user: any, query: NotificationQueryDto): Promise<{
        unreadCount: number;
        notifications: {
            id: string;
            type: string;
            title: string;
            content: string;
            isRead: boolean;
            createdAt: string;
            data: string | number | true | import(".prisma/client").Prisma.JsonObject | import(".prisma/client").Prisma.JsonArray;
        }[];
        pagination: {
            total: number;
            page: number | undefined;
            pageSize: number | undefined;
            hasMore: boolean;
        };
    }>;
    markNotificationAsRead(user: any, notificationId: string): Promise<{
        id: string;
        isRead: boolean;
        message: string;
    }>;
    markAllNotificationsAsRead(user: any): Promise<{
        success: boolean;
        count: number;
        message: string;
    }>;
    deleteNotification(user: any, notificationId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    getWallet(user: any): Promise<{
        balance: number;
        currency: string;
        pendingTransactions: number;
        transactions: {
            id: string;
            type: string;
            amount: number;
            currency: string;
            status: string;
            description: string;
            createdAt: string;
            reference: string;
        }[];
        cards: {
            id: string;
            type: any;
            brand: any;
            last4: any;
            expiryMonth: number;
            expiryYear: number;
            isDefault: boolean;
        }[];
    }>;
    depositToWallet(user: any, depositDto: WalletDepositDto): Promise<{
        transactionId: string;
        amount: number;
        currency: string;
        status: string;
        paymentIntent: {
            id: string;
            clientSecret: string;
        };
        message: string;
    }>;
    getCoupons(user: any, query: CouponQueryDto): Promise<{
        coupons: {
            id: string;
            code: string;
            type: string;
            value: number;
            minPurchase: number | null;
            maxDiscount: number | null;
            currency: string;
            description: string;
            validFrom: string;
            validUntil: string;
            status: string;
            restrictions: string | number | true | import(".prisma/client").Prisma.JsonObject | import(".prisma/client").Prisma.JsonArray;
        }[];
    }>;
}
