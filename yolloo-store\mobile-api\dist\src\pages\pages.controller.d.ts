import { PagesService } from './pages.service';
import { PageConfigQueryDto } from './dto/page-config-query.dto';
import { PageContentQueryDto } from './dto/page-content-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class PagesController {
    private readonly pagesService;
    constructor(pagesService: PagesService);
    getPageConfigs(query: PageConfigQueryDto, ctx: RequestContext): Promise<{
        pages: {
            id: string;
            title: string;
            description: string;
            icon: string;
            type: string;
            category: string;
            position: number;
            isActive: boolean;
            requiresAuth: boolean;
            url: string;
            metadata: {
                lastUpdated: string;
                version: string;
                cacheTime: number;
            };
        }[];
        total: number;
        layout: "grid" | "list";
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getPageContent(query: PageContentQueryDto, ctx: RequestContext): Promise<{
        error: string;
        pageId: string;
        availablePages: string[];
        title?: undefined;
        content?: undefined;
        styles?: undefined;
        metadata?: undefined;
        assets?: undefined;
    } | {
        pageId: string;
        title: any;
        content: any;
        styles: any;
        metadata: {
            language: string;
            theme: string;
            currency: string;
            lastUpdated: string;
            version: string;
        };
        assets: {
            scripts: never[];
            stylesheets: never[];
            images: never[];
        } | undefined;
        error?: undefined;
        availablePages?: undefined;
    } | {
        pageId: string;
        title: string;
        content: string;
        styles: any;
        metadata: {
            language: string;
            theme: string;
            currency: string;
            lastUpdated: string;
            version: string;
            sourceType: string;
            sourceProductId?: undefined;
        };
        assets: {
            scripts: never[];
            stylesheets: never[];
            images: never[];
        } | undefined;
    } | {
        pageId: string;
        title: any;
        content: string;
        styles: string;
        metadata: {
            language: string;
            theme: string;
            currency: string;
            lastUpdated: string;
            version: string;
            sourceProductId: string;
            sourceType: string;
        };
        assets: {
            scripts: never[];
            stylesheets: never[];
            images: string[];
        } | undefined;
    }>;
}
