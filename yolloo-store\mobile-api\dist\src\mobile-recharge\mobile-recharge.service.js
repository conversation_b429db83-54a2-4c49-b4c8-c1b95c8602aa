"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MobileRechargeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MobileRechargeService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const utils_1 = require("../common/utils");
let MobileRechargeService = MobileRechargeService_1 = class MobileRechargeService {
    prisma;
    logger = new common_1.Logger(MobileRechargeService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getRechargeOptions(query, ctx) {
        console.log('Context in getRechargeOptions:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const products = await this.prisma.product.findMany({
                where: {
                    AND: [
                        { status: 'ACTIVE' },
                        { off_shelve: false },
                        {
                            OR: [
                                { name: { contains: '充值', mode: 'insensitive' } },
                                { name: { contains: 'recharge', mode: 'insensitive' } },
                                { name: { contains: 'topup', mode: 'insensitive' } },
                                { name: { contains: 'credit', mode: 'insensitive' } },
                                { description: { contains: '充值', mode: 'insensitive' } },
                                { description: { contains: 'recharge', mode: 'insensitive' } },
                            ]
                        }
                    ]
                },
                include: {
                    category: true,
                    variants: {
                        orderBy: {
                            price: 'asc',
                        },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
            });
            if (products.length === 0) {
                this.logger.warn('No recharge products found in database, using fallback data');
                return this.getFallbackRechargeOptions(query, ctx);
            }
            this.logger.log(`Found ${products.length} recharge products`);
            const rechargeOptions = products.map((product, index) => {
                const variant = product.variants[0];
                const amount = variant ? parseFloat(variant.price.toString()) : product.price;
                const discount = Math.floor(amount * 0.05);
                const finalAmount = amount - discount;
                let operator = 'china-mobile';
                let operatorName = isZh ? '中国移动' : 'China Mobile';
                const productName = product.name.toLowerCase();
                if (productName.includes('联通') || productName.includes('unicom')) {
                    operator = 'china-unicom';
                    operatorName = isZh ? '中国联通' : 'China Unicom';
                }
                else if (productName.includes('电信') || productName.includes('telecom')) {
                    operator = 'china-telecom';
                    operatorName = isZh ? '中国电信' : 'China Telecom';
                }
                return {
                    id: product.id,
                    operator: operator,
                    operatorName: operatorName,
                    amount: amount,
                    currency: variant?.currency || 'USD',
                    discount: discount,
                    finalAmount: finalAmount,
                    accountType: 'prepaid',
                    description: product.description || (isZh ? `${amount}元话费充值` : `${amount} Yuan Credit Recharge`),
                    processingTime: isZh ? '即时到账' : 'Instant',
                    imageUrl: product.images[0] || `https://example.com/${operator}.jpg`,
                    isPopular: index === 0,
                };
            });
            let filteredOptions = rechargeOptions;
            if (query.operator) {
                filteredOptions = rechargeOptions.filter(option => option.operator === query.operator);
            }
            if (query.accountType) {
                filteredOptions = filteredOptions.filter(option => option.accountType === query.accountType);
            }
            if (query.sortBy === 'amount') {
                filteredOptions.sort((a, b) => {
                    return query.sortOrder === 'desc' ? b.amount - a.amount : a.amount - b.amount;
                });
            }
            const total = filteredOptions.length;
            const skip = (query.page - 1) * query.pageSize;
            const paginatedOptions = filteredOptions.slice(skip, skip + query.pageSize);
            return {
                rechargeOptions: paginatedOptions,
                pagination: {
                    total,
                    page: query.page,
                    pageSize: query.pageSize,
                    hasMore: skip + paginatedOptions.length < total,
                },
                operators: await this.getOperators(isZh),
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching recharge options:', error);
            return this.getFallbackRechargeOptions(query, ctx);
        }
    }
    async getFallbackRechargeOptions(query, ctx) {
        const isZh = ctx.language.startsWith('zh');
        const rechargeOptions = [
            {
                id: '1',
                operator: 'china-mobile',
                operatorName: isZh ? '中国移动' : 'China Mobile',
                amount: 10,
                currency: ctx.currency,
                discount: 0,
                finalAmount: 10,
                accountType: 'prepaid',
                description: isZh ? '10元话费充值' : '10 Yuan Credit Recharge',
                processingTime: isZh ? '即时到账' : 'Instant',
                imageUrl: 'https://example.com/china-mobile.jpg',
            },
            {
                id: '2',
                operator: 'china-mobile',
                operatorName: isZh ? '中国移动' : 'China Mobile',
                amount: 20,
                currency: ctx.currency,
                discount: 1,
                finalAmount: 19,
                accountType: 'prepaid',
                description: isZh ? '20元话费充值' : '20 Yuan Credit Recharge',
                processingTime: isZh ? '即时到账' : 'Instant',
                imageUrl: 'https://example.com/china-mobile.jpg',
                isPopular: true,
            },
        ];
        let filteredOptions = rechargeOptions;
        if (query.operator) {
            filteredOptions = rechargeOptions.filter(option => option.operator === query.operator);
        }
        if (query.accountType) {
            filteredOptions = filteredOptions.filter(option => option.accountType === query.accountType);
        }
        const total = filteredOptions.length;
        const skip = (query.page - 1) * query.pageSize;
        const paginatedOptions = filteredOptions.slice(skip, skip + query.pageSize);
        return {
            rechargeOptions: paginatedOptions,
            pagination: {
                total,
                page: query.page,
                pageSize: query.pageSize,
                hasMore: skip + paginatedOptions.length < total,
            },
            operators: await this.getOperators(isZh),
            context: {
                language: ctx.language,
                theme: ctx.theme,
                currency: ctx.currency,
            },
        };
    }
    async createRechargeOrder(orderData, ctx) {
        console.log('Context in createRechargeOrder:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            let product = await this.prisma.product.findFirst({
                where: {
                    AND: [
                        { status: 'ACTIVE' },
                        { off_shelve: false },
                        {
                            OR: [
                                { name: { contains: '充值', mode: 'insensitive' } },
                                { name: { contains: 'recharge', mode: 'insensitive' } },
                                { name: { contains: 'topup', mode: 'insensitive' } },
                            ]
                        }
                    ]
                },
                include: {
                    variants: {
                        orderBy: {
                            price: 'asc',
                        },
                    },
                },
            });
            if (!product) {
                this.logger.warn('No recharge product found for order creation, using default product');
                product = {
                    id: 'mobile-recharge-default',
                    name: 'Mobile Recharge',
                    description: 'Mobile phone credit recharge service',
                    price: 0,
                    variants: [],
                };
            }
            else {
                this.logger.log(`Using product for recharge order: ${product.name}`);
            }
            const variant = product?.variants?.[0];
            const productPrice = variant ? parseFloat(variant.price.toString()) : (product?.price || 0);
            let userId;
            let anonymousUser = await this.prisma.user.findFirst({
                where: { email: '<EMAIL>' }
            });
            if (!anonymousUser) {
                anonymousUser = await this.prisma.user.create({
                    data: {
                        email: '<EMAIL>',
                        name: 'Anonymous User',
                        hashedPassword: 'anonymous',
                    }
                });
            }
            userId = anonymousUser.id;
            const order = await this.prisma.order.create({
                data: {
                    userId: userId,
                    status: 'PROCESSING',
                    total: orderData.amount,
                },
            });
            await this.prisma.orderItem.create({
                data: {
                    orderId: order.id,
                    productCode: product?.id || 'mobile-recharge-default',
                    variantCode: variant?.id || 'default',
                    variantText: `${orderData.operator} ${orderData.amount} recharge`,
                    quantity: 1,
                    price: orderData.amount,
                },
            });
            const orderResponse = {
                id: order.id,
                phoneNumber: orderData.phoneNumber,
                operator: orderData.operator,
                amount: orderData.amount,
                currency: ctx.currency,
                status: 'pending',
                statusText: isZh ? '处理中' : 'Processing',
                createdAt: utils_1.DateFormatter.iso(order.createdAt),
                estimatedCompletionTime: utils_1.DateFormatter.iso(utils_1.DateUtils.addMinutes(new Date(), 5)),
                description: isZh ? `${orderData.amount}元话费充值` : `${orderData.amount} Yuan Credit Recharge`,
            };
            return {
                order: orderResponse,
                message: isZh ? '充值订单创建成功，正在处理中' : 'Recharge order created successfully, processing',
            };
        }
        catch (error) {
            this.logger.error('Error creating recharge order:', error);
            const order = {
                id: `recharge_${Date.now()}`,
                phoneNumber: orderData.phoneNumber,
                operator: orderData.operator,
                amount: orderData.amount,
                currency: ctx.currency,
                status: 'pending',
                statusText: isZh ? '处理中' : 'Processing',
                createdAt: utils_1.DateFormatter.iso(new Date()),
                estimatedCompletionTime: utils_1.DateFormatter.iso(utils_1.DateUtils.addMinutes(new Date(), 5)),
                description: isZh ? `${orderData.amount}元话费充值` : `${orderData.amount} Yuan Credit Recharge`,
            };
            return {
                order,
                message: isZh ? '充值订单创建成功，正在处理中' : 'Recharge order created successfully, processing',
            };
        }
    }
    async getRechargeHistory(userId, query, ctx) {
        console.log('Context in getRechargeHistory:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const rechargeHistories = await this.prisma.rechargeHistory.findMany({
                where: {
                    userId: userId,
                },
                include: {
                    operator: true,
                },
                orderBy: {
                    createdAt: 'desc',
                },
                skip: (query.page - 1) * query.pageSize,
                take: query.pageSize,
            });
            if (rechargeHistories.length > 0) {
                const total = await this.prisma.rechargeHistory.count({
                    where: { userId: userId },
                });
                const history = rechargeHistories.map(record => ({
                    id: record.id,
                    phoneNumber: record.phoneNumber,
                    operator: record.operator.code,
                    operatorName: isZh ? record.operator.nameZh : record.operator.nameEn,
                    amount: record.amount,
                    currency: record.currency,
                    status: record.status.toLowerCase(),
                    statusText: this.getStatusText(record.status, isZh),
                    createdAt: utils_1.DateFormatter.iso(record.createdAt),
                    completedAt: record.completedAt ? utils_1.DateFormatter.iso(record.completedAt) : null,
                    failureReason: record.status === 'FAILED' ? (isZh ? '处理失败' : 'Processing failed') : null,
                }));
                return {
                    history,
                    pagination: {
                        total,
                        page: query.page,
                        pageSize: query.pageSize,
                        hasMore: (query.page * query.pageSize) < total,
                    },
                };
            }
            const orders = await this.prisma.order.findMany({
                where: {
                    AND: [
                        userId ? { userId: userId } : {},
                        {
                            items: {
                                some: {
                                    variantText: {
                                        contains: 'recharge'
                                    }
                                }
                            }
                        }
                    ]
                },
                include: {
                    items: true,
                },
                orderBy: {
                    createdAt: 'desc',
                },
                skip: (query.page - 1) * query.pageSize,
                take: query.pageSize,
            });
            const total = await this.prisma.order.count({
                where: {
                    AND: [
                        userId ? { userId: userId } : {},
                        {
                            items: {
                                some: {
                                    variantText: {
                                        contains: 'recharge'
                                    }
                                }
                            }
                        }
                    ]
                },
            });
            const history = orders.map(order => {
                const rechargeItem = order.items.find(item => item.variantText?.includes('recharge'));
                let operator = 'china-mobile';
                let phoneNumber = '138****8888';
                if (rechargeItem?.variantText) {
                    const variantText = rechargeItem.variantText;
                    if (variantText.includes('china-unicom')) {
                        operator = 'china-unicom';
                    }
                    else if (variantText.includes('china-telecom')) {
                        operator = 'china-telecom';
                    }
                }
                let operatorName = isZh ? '未知运营商' : 'Unknown Operator';
                if (operator === 'china-mobile') {
                    operatorName = isZh ? '中国移动' : 'China Mobile';
                }
                else if (operator === 'china-unicom') {
                    operatorName = isZh ? '中国联通' : 'China Unicom';
                }
                else if (operator === 'china-telecom') {
                    operatorName = isZh ? '中国电信' : 'China Telecom';
                }
                let statusText = isZh ? '未知状态' : 'Unknown Status';
                if (order.status === 'DELIVERED') {
                    statusText = isZh ? '充值成功' : 'Completed';
                }
                else if (order.status === 'PROCESSING') {
                    statusText = isZh ? '处理中' : 'Processing';
                }
                else if (order.status === 'CANCELLED') {
                    statusText = isZh ? '已取消' : 'Cancelled';
                }
                else if (order.status === 'REFUNDED') {
                    statusText = isZh ? '充值失败' : 'Failed';
                }
                return {
                    id: order.id,
                    phoneNumber: phoneNumber,
                    operator: operator,
                    operatorName: operatorName,
                    amount: parseFloat(order.total.toString()),
                    currency: ctx.currency,
                    status: order.status.toLowerCase(),
                    statusText: statusText,
                    createdAt: utils_1.DateFormatter.iso(order.createdAt),
                    completedAt: order.updatedAt ? utils_1.DateFormatter.iso(order.updatedAt) : null,
                    failureReason: order.status === 'REFUNDED' ? (isZh ? '处理失败' : 'Processing failed') : null,
                };
            });
            return {
                history,
                pagination: {
                    total,
                    page: query.page,
                    pageSize: query.pageSize,
                    hasMore: (query.page * query.pageSize) < total,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching recharge history:', error);
            const history = [
                {
                    id: 'recharge_001',
                    phoneNumber: '138****8888',
                    operator: 'china-mobile',
                    operatorName: isZh ? '中国移动' : 'China Mobile',
                    amount: 20,
                    currency: ctx.currency,
                    status: 'completed',
                    statusText: isZh ? '充值成功' : 'Completed',
                    createdAt: '2023-12-01T10:30:00Z',
                    completedAt: '2023-12-01T10:31:00Z',
                },
            ];
            return {
                history,
                pagination: {
                    total: history.length,
                    page: query.page,
                    pageSize: query.pageSize,
                    hasMore: false,
                },
            };
        }
    }
    async getOperators(isZh) {
        try {
            const operators = await this.prisma.mobileOperator.findMany({
                where: {
                    isActive: true,
                },
                include: {
                    country: true,
                },
                orderBy: {
                    nameEn: 'asc',
                },
            });
            if (operators.length > 0) {
                return operators.map(operator => ({
                    id: operator.code,
                    name: isZh ? operator.nameZh : operator.nameEn,
                    logo: operator.logoUrl || `https://example.com/${operator.code}.jpg`,
                    country: isZh ? operator.country.nameZh : operator.country.nameEn,
                }));
            }
            return [
                { id: 'china-mobile', name: isZh ? '中国移动' : 'China Mobile', logo: 'https://example.com/china-mobile.jpg' },
                { id: 'china-unicom', name: isZh ? '中国联通' : 'China Unicom', logo: 'https://example.com/china-unicom.jpg' },
                { id: 'china-telecom', name: isZh ? '中国电信' : 'China Telecom', logo: 'https://example.com/china-telecom.jpg' },
            ];
        }
        catch (error) {
            this.logger.error('Error fetching operators:', error);
            return [
                { id: 'china-mobile', name: isZh ? '中国移动' : 'China Mobile', logo: 'https://example.com/china-mobile.jpg' },
                { id: 'china-unicom', name: isZh ? '中国联通' : 'China Unicom', logo: 'https://example.com/china-unicom.jpg' },
                { id: 'china-telecom', name: isZh ? '中国电信' : 'China Telecom', logo: 'https://example.com/china-telecom.jpg' },
            ];
        }
    }
    getStatusText(status, isZh) {
        const statusMap = {
            'PENDING': isZh ? '处理中' : 'Pending',
            'PROCESSING': isZh ? '处理中' : 'Processing',
            'COMPLETED': isZh ? '充值成功' : 'Completed',
            'FAILED': isZh ? '充值失败' : 'Failed',
            'CANCELLED': isZh ? '已取消' : 'Cancelled',
        };
        return statusMap[status] || (isZh ? '未知状态' : 'Unknown Status');
    }
};
MobileRechargeService = MobileRechargeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], MobileRechargeService);
exports.MobileRechargeService = MobileRechargeService;
//# sourceMappingURL=mobile-recharge.service.js.map