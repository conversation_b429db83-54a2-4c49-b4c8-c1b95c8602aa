# 迁移配置环境变量
# 这些变量控制启动脚本中的迁移行为

# 是否启用自动迁移修复 (true/false)
AUTO_FIX_MIGRATIONS=true

# 是否在迁移失败时自动清理失败的迁移记录 (true/false)
AUTO_CLEAN_FAILED_MIGRATIONS=true

# 是否允许应用 schema 差异迁移 (true/false)
ALLOW_SCHEMA_DIFF_MIGRATION=true

# 是否在应用迁移前创建备份 (true/false)
BACKUP_BEFORE_MIGRATION=true

# 迁移超时时间（秒）
MIGRATION_TIMEOUT=300

# 是否在迁移失败时继续启动应用 (true/false)
# 注意：设置为 true 可能导致应用与数据库 schema 不匹配
CONTINUE_ON_MIGRATION_FAILURE=false

# 是否启用详细的迁移日志 (true/false)
VERBOSE_MIGRATION_LOGS=true

# 危险操作检测关键词（用逗号分隔）
DANGEROUS_OPERATIONS="DROP TABLE,DROP DATABASE,TRUNCATE,DELETE FROM"

# 是否允许在生产环境中重置迁移状态 (true/false)
# 警告：这是极其危险的操作，会丢失所有数据
ALLOW_PRODUCTION_RESET=false
