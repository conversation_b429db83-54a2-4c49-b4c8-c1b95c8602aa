#!/bin/sh

# 迁移修复脚本
# 用于处理生产环境中的复杂迁移问题

set -e

echo "=== Migration Fix Script ==="

# 检查必要的环境变量
if [ -z "$DATABASE_URL" ]; then
    echo "ERROR: DATABASE_URL environment variable is not set"
    exit 1
fi

# 函数：备份迁移表
backup_migration_table() {
    echo "Creating backup of migration table..."
    psql "$DATABASE_URL" -c "CREATE TABLE IF NOT EXISTS _prisma_migrations_backup AS SELECT * FROM _prisma_migrations;" 2>/dev/null || true
    echo "✓ Migration table backup created"
}

# 函数：列出所有失败的迁移
list_failed_migrations() {
    echo "=== Checking for Failed Migrations ==="
    
    FAILED_MIGRATIONS=$(psql "$DATABASE_URL" -t -c "SELECT migration_name FROM _prisma_migrations WHERE finished_at IS NULL;" 2>/dev/null | tr -d ' ')
    
    if [ -n "$FAILED_MIGRATIONS" ]; then
        echo "Found failed migrations:"
        echo "$FAILED_MIGRATIONS"
        return 0
    else
        echo "No failed migrations found"
        return 1
    fi
}

# 函数：清理特定的失败迁移
clean_failed_migration() {
    local migration_name="$1"
    
    if [ -z "$migration_name" ]; then
        echo "ERROR: Migration name not provided"
        return 1
    fi
    
    echo "Cleaning failed migration: $migration_name"
    
    # 删除失败的迁移记录
    psql "$DATABASE_URL" -c "DELETE FROM _prisma_migrations WHERE migration_name = '$migration_name';" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✓ Successfully cleaned migration: $migration_name"
        return 0
    else
        echo "✗ Failed to clean migration: $migration_name"
        return 1
    fi
}

# 函数：重置迁移状态（危险操作，仅用于开发环境）
reset_migration_state() {
    if [ "$NODE_ENV" = "production" ]; then
        echo "ERROR: Cannot reset migration state in production"
        return 1
    fi
    
    echo "⚠️  DANGER: Resetting migration state (development only)"
    psql "$DATABASE_URL" -c "TRUNCATE TABLE _prisma_migrations;" 2>/dev/null
    echo "Migration state reset"
}

# 函数：验证 schema 一致性
verify_schema_consistency() {
    echo "=== Verifying Schema Consistency ==="
    
    # 尝试生成当前数据库的 schema
    npx prisma db pull --print > /tmp/current_schema.prisma 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✓ Successfully pulled current database schema"
        
        # 比较 schema 文件
        if diff -q prisma/schema.prisma /tmp/current_schema.prisma > /dev/null 2>&1; then
            echo "✓ Schema files are consistent"
            rm -f /tmp/current_schema.prisma
            return 0
        else
            echo "⚠️  Schema files differ"
            echo "Differences:"
            diff prisma/schema.prisma /tmp/current_schema.prisma || true
            rm -f /tmp/current_schema.prisma
            return 1
        fi
    else
        echo "✗ Failed to pull current database schema"
        return 1
    fi
}

# 函数：生成修复迁移
generate_fix_migration() {
    echo "=== Generating Fix Migration ==="
    
    # 生成从当前数据库到目标 schema 的差异
    DIFF_SQL=$(npx prisma migrate diff --from-url "$DATABASE_URL" --to-schema-datamodel prisma/schema.prisma --script 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$DIFF_SQL" ]; then
        echo "Generated schema differences:"
        echo "$DIFF_SQL"
        
        # 保存到文件
        echo "$DIFF_SQL" > /tmp/fix_migration.sql
        echo "✓ Fix migration saved to /tmp/fix_migration.sql"
        
        # 检查是否包含危险操作
        if echo "$DIFF_SQL" | grep -qi "DROP TABLE\|DROP DATABASE\|TRUNCATE\|DELETE FROM"; then
            echo "⚠️  WARNING: Migration contains potentially dangerous operations"
            echo "Please review carefully before applying"
            return 1
        fi
        
        return 0
    else
        echo "No schema differences found or failed to generate diff"
        return 1
    fi
}

# 函数：应用修复迁移
apply_fix_migration() {
    if [ ! -f "/tmp/fix_migration.sql" ]; then
        echo "ERROR: Fix migration file not found"
        return 1
    fi
    
    echo "=== Applying Fix Migration ==="
    echo "Executing SQL:"
    cat /tmp/fix_migration.sql
    
    # 应用迁移
    psql "$DATABASE_URL" -f /tmp/fix_migration.sql
    
    if [ $? -eq 0 ]; then
        echo "✓ Fix migration applied successfully"
        rm -f /tmp/fix_migration.sql
        return 0
    else
        echo "✗ Failed to apply fix migration"
        return 1
    fi
}

# 主执行逻辑
main() {
    case "${1:-auto}" in
        "backup")
            backup_migration_table
            ;;
        "list")
            list_failed_migrations
            ;;
        "clean")
            if [ -z "$2" ]; then
                echo "Usage: $0 clean <migration_name>"
                exit 1
            fi
            clean_failed_migration "$2"
            ;;
        "reset")
            reset_migration_state
            ;;
        "verify")
            verify_schema_consistency
            ;;
        "generate")
            generate_fix_migration
            ;;
        "apply")
            apply_fix_migration
            ;;
        "auto")
            echo "Running automatic migration fix..."
            backup_migration_table
            
            if list_failed_migrations; then
                # 有失败的迁移，尝试清理
                FAILED_MIGRATIONS=$(psql "$DATABASE_URL" -t -c "SELECT migration_name FROM _prisma_migrations WHERE finished_at IS NULL;" 2>/dev/null | tr -d ' ')
                for migration in $FAILED_MIGRATIONS; do
                    clean_failed_migration "$migration"
                done
            fi
            
            # 检查 schema 一致性
            if ! verify_schema_consistency; then
                echo "Schema inconsistency detected, generating fix..."
                if generate_fix_migration; then
                    echo "Fix migration generated, applying..."
                    apply_fix_migration
                fi
            fi
            ;;
        *)
            echo "Usage: $0 {backup|list|clean|reset|verify|generate|apply|auto}"
            echo ""
            echo "Commands:"
            echo "  backup   - Create backup of migration table"
            echo "  list     - List failed migrations"
            echo "  clean    - Clean specific failed migration"
            echo "  reset    - Reset migration state (dev only)"
            echo "  verify   - Verify schema consistency"
            echo "  generate - Generate fix migration"
            echo "  apply    - Apply fix migration"
            echo "  auto     - Run automatic fix (default)"
            exit 1
            ;;
    esac
}

main "$@"
