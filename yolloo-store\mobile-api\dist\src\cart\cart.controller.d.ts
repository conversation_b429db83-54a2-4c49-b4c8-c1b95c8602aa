import { CartService } from './cart.service';
import { AddCartItemDto } from './dto/add-cart-item.dto';
import { UpdateCartItemDto } from './dto/update-cart-item.dto';
export declare class CartController {
    private readonly cartService;
    constructor(cartService: CartService);
    getCart(user: any): Promise<{
        items: {
            id: string;
            productId: string;
            variantId: string | null;
            name: string;
            price: number;
            currency: string;
            quantity: number;
            imageUrl: string;
            attributes: {
                dataSize: number;
                duration: number;
                countries: any;
            };
        }[];
        summary: {
            subtotal: number;
            shipping: number;
            tax: number;
            total: number;
            currency: string;
        };
    }>;
    addCartItem(user: any, addCartItemDto: AddCartItemDto): Promise<{
        id: any;
        productId: any;
        variantId: any;
        quantity: any;
        message: string;
    }>;
    updateCartItem(user: any, itemId: string, updateCartItemDto: UpdateCartItemDto): Promise<{
        id: string;
        quantity: number;
        message: string;
    }>;
    removeCartItem(user: any, itemId: string): Promise<{
        message: string;
    }>;
}
