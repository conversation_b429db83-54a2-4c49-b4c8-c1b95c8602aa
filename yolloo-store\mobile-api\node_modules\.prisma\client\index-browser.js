
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
} = require('@prisma/client/runtime/index-browser')


const Prisma = {}

exports.Prisma = Prisma

/**
 * Prisma Client JS version: 4.16.2
 * Query Engine version: 4bc8b6e1b66cb932731fb1bdbbc550d1e010de81
 */
Prisma.prismaVersion = {
  client: "4.16.2",
  engine: "4bc8b6e1b66cb932731fb1bdbbc550d1e010de81"
}

Prisma.PrismaClientKnownRequestError = () => {
  throw new Error(`PrismaClientKnownRequestError is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  throw new Error(`PrismaClientUnknownRequestError is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.PrismaClientRustPanicError = () => {
  throw new Error(`PrismaClientRustPanicError is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.PrismaClientInitializationError = () => {
  throw new Error(`PrismaClientInitializationError is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.PrismaClientValidationError = () => {
  throw new Error(`PrismaClientValidationError is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.NotFoundError = () => {
  throw new Error(`NotFoundError is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  throw new Error(`sqltag is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.empty = () => {
  throw new Error(`empty is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.join = () => {
  throw new Error(`join is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.raw = () => {
  throw new Error(`raw is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  throw new Error(`Extensions.getExtensionContext is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}
Prisma.defineExtension = () => {
  throw new Error(`Extensions.defineExtension is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}

/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  image: 'image',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  hashedPassword: 'hashedPassword'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  websiteDescription: 'websiteDescription',
  price: 'price',
  images: 'images',
  categoryId: 'categoryId',
  stock: 'stock',
  specifications: 'specifications',
  status: 'status',
  sku: 'sku',
  requiredUID: 'requiredUID',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  mcc: 'mcc',
  off_shelve: 'off_shelve',
  dataSize: 'dataSize',
  planType: 'planType',
  country: 'country',
  countryCode: 'countryCode',
  odooLastSyncAt: 'odooLastSyncAt',
  popularityScore: 'popularityScore',
  isPopular: 'isPopular'
};

exports.Prisma.ProductVariantScalarFieldEnum = {
  id: 'id',
  price: 'price',
  currency: 'currency',
  productId: 'productId',
  variantCode: 'variantCode',
  duration: 'duration',
  durationType: 'durationType',
  attributes: 'attributes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductParameterScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  value: 'value',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  image: 'image',
  parentId: 'parentId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CartItemScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  productId: 'productId',
  variantId: 'variantId',
  quantity: 'quantity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WishlistItemScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  productId: 'productId',
  createdAt: 'createdAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  total: 'total',
  status: 'status',
  addressId: 'addressId',
  shippingAddressSnapshot: 'shippingAddressSnapshot',
  paymentId: 'paymentId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  referralCode: 'referralCode'
};

exports.Prisma.OdooOrderStatusScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  variantCode: 'variantCode',
  odooOrderRef: 'odooOrderRef',
  status: 'status',
  description: 'description',
  productName: 'productName',
  isDigital: 'isDigital',
  deliveredQty: 'deliveredQty',
  trackingNumber: 'trackingNumber',
  planState: 'planState',
  uid: 'uid',
  lastCheckedAt: 'lastCheckedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  currency: 'currency',
  status: 'status',
  provider: 'provider',
  paymentMethod: 'paymentMethod',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AddressScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  name: 'name',
  phone: 'phone',
  address1: 'address1',
  address2: 'address2',
  city: 'city',
  state: 'state',
  postalCode: 'postalCode',
  country: 'country',
  isDefault: 'isDefault',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  productId: 'productId',
  productCode: 'productCode',
  variantCode: 'variantCode',
  variantText: 'variantText',
  quantity: 'quantity',
  price: 'price',
  uid: 'uid',
  lpaString: 'lpaString'
};

exports.Prisma.ReviewScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  productId: 'productId',
  rating: 'rating',
  comment: 'comment',
  createdAt: 'createdAt'
};

exports.Prisma.SettingsScalarFieldEnum = {
  id: 'id',
  siteName: 'siteName',
  language: 'language',
  theme: 'theme',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.YollooCardScalarFieldEnum = {
  id: 'id',
  number: 'number',
  status: 'status',
  type: 'type',
  customName: 'customName',
  activationDate: 'activationDate',
  expiryDate: 'expiryDate',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EsimScalarFieldEnum = {
  id: 'id',
  iccid: 'iccid',
  status: 'status',
  activationDate: 'activationDate',
  expiryDate: 'expiryDate',
  yollooCardId: 'yollooCardId',
  productId: 'productId',
  profileId: 'profileId',
  planId: 'planId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EsimProfileScalarFieldEnum = {
  id: 'id',
  imsi: 'imsi',
  carrierName: 'carrierName',
  networkType: 'networkType',
  serviceArea: 'serviceArea',
  roamingEnabled: 'roamingEnabled',
  status: 'status',
  activationDate: 'activationDate',
  expiryDate: 'expiryDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EsimPlanScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  dataLimit: 'dataLimit',
  duration: 'duration',
  price: 'price',
  currency: 'currency',
  features: 'features',
  roamingRegions: 'roamingRegions',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AffiliateProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  code: 'code',
  commissionRate: 'commissionRate',
  discountRate: 'discountRate',
  totalEarnings: 'totalEarnings',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  organizationId: 'organizationId',
  isAdmin: 'isAdmin'
};

exports.Prisma.AffiliateReferralScalarFieldEnum = {
  id: 'id',
  affiliateId: 'affiliateId',
  orderId: 'orderId',
  commissionAmount: 'commissionAmount',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  organizationCommissionId: 'organizationCommissionId'
};

exports.Prisma.AffiliateWithdrawalScalarFieldEnum = {
  id: 'id',
  affiliateId: 'affiliateId',
  amount: 'amount',
  status: 'status',
  paymentMethod: 'paymentMethod',
  paymentDetails: 'paymentDetails',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AffiliateVisitScalarFieldEnum = {
  id: 'id',
  affiliateId: 'affiliateId',
  source: 'source',
  path: 'path',
  userAgent: 'userAgent',
  referrer: 'referrer',
  createdAt: 'createdAt',
  convertedToOrder: 'convertedToOrder',
  orderId: 'orderId',
  organizationId: 'organizationId'
};

exports.Prisma.PreSaleSubscriptionScalarFieldEnum = {
  id: 'id',
  email: 'email',
  referralCode: 'referralCode',
  status: 'status',
  discountCode: 'discountCode',
  subscribedAt: 'subscribedAt',
  updatedAt: 'updatedAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  convertedToUser: 'convertedToUser',
  userId: 'userId'
};

exports.Prisma.AffiliateOrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  code: 'code',
  logo: 'logo',
  commissionRate: 'commissionRate',
  discountRate: 'discountRate',
  totalEarnings: 'totalEarnings',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationWithdrawalScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  amount: 'amount',
  status: 'status',
  paymentMethod: 'paymentMethod',
  paymentDetails: 'paymentDetails',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationCommissionScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  commissionAmount: 'commissionAmount',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationInviteScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  affiliateId: 'affiliateId',
  email: 'email',
  status: 'status',
  inviteCode: 'inviteCode',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  commissionRate: 'commissionRate',
  isAdmin: 'isAdmin'
};

exports.Prisma.PasswordResetTokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  userId: 'userId',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt'
};

exports.Prisma.UserLoginHistoryScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  loginTimestamp: 'loginTimestamp',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  loginMethod: 'loginMethod',
  isSuccessful: 'isSuccessful',
  additionalInfo: 'additionalInfo',
  createdAt: 'createdAt'
};

exports.Prisma.MarketingCommissionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  amount: 'amount',
  status: 'status',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SyncLogScalarFieldEnum = {
  id: 'id',
  syncId: 'syncId',
  level: 'level',
  message: 'message',
  data: 'data',
  timestamp: 'timestamp'
};

exports.Prisma.SyncTaskScalarFieldEnum = {
  id: 'id',
  syncId: 'syncId',
  status: 'status',
  options: 'options',
  stats: 'stats',
  startTime: 'startTime',
  endTime: 'endTime',
  duration: 'duration',
  createdBy: 'createdBy'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  title: 'title',
  content: 'content',
  data: 'data',
  isRead: 'isRead',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WalletScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  balance: 'balance',
  currency: 'currency',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  walletId: 'walletId',
  type: 'type',
  amount: 'amount',
  currency: 'currency',
  description: 'description',
  status: 'status',
  reference: 'reference',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaymentCardScalarFieldEnum = {
  id: 'id',
  walletId: 'walletId',
  cardNumber: 'cardNumber',
  cardHolder: 'cardHolder',
  expiryMonth: 'expiryMonth',
  expiryYear: 'expiryYear',
  cardType: 'cardType',
  type: 'type',
  brand: 'brand',
  last4: 'last4',
  isDefault: 'isDefault',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CouponScalarFieldEnum = {
  id: 'id',
  code: 'code',
  type: 'type',
  value: 'value',
  minPurchase: 'minPurchase',
  maxDiscount: 'maxDiscount',
  currency: 'currency',
  description: 'description',
  validFrom: 'validFrom',
  validUntil: 'validUntil',
  usageLimit: 'usageLimit',
  usedCount: 'usedCount',
  status: 'status',
  restrictions: 'restrictions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserCouponScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  couponId: 'couponId',
  usedAt: 'usedAt',
  orderId: 'orderId'
};

exports.Prisma.PackageUsageScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  orderId: 'orderId',
  packageId: 'packageId',
  packageName: 'packageName',
  totalData: 'totalData',
  usedData: 'usedData',
  remainingData: 'remainingData',
  validUntil: 'validUntil',
  status: 'status',
  lastUsedAt: 'lastUsedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RechargeHistoryScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  amount: 'amount',
  currency: 'currency',
  method: 'method',
  status: 'status',
  reference: 'reference',
  phoneNumber: 'phoneNumber',
  operatorId: 'operatorId',
  completedAt: 'completedAt',
  createdAt: 'createdAt'
};

exports.Prisma.SocialAccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  provider: 'provider',
  providerId: 'providerId',
  providerAccountId: 'providerAccountId',
  email: 'email',
  name: 'name',
  avatar: 'avatar',
  picture: 'picture',
  lastLoginAt: 'lastLoginAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductViewScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  productId: 'productId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  viewedAt: 'viewedAt'
};

exports.Prisma.UploadScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  filename: 'filename',
  originalName: 'originalName',
  mimeType: 'mimeType',
  size: 'size',
  url: 'url',
  path: 'path',
  category: 'category',
  uploadedBy: 'uploadedBy',
  createdAt: 'createdAt'
};

exports.Prisma.ContinentScalarFieldEnum = {
  id: 'id',
  code: 'code',
  nameEn: 'nameEn',
  nameZh: 'nameZh',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CountryScalarFieldEnum = {
  id: 'id',
  code: 'code',
  nameEn: 'nameEn',
  nameZh: 'nameZh',
  continentId: 'continentId',
  flagUrl: 'flagUrl',
  currency: 'currency',
  timezone: 'timezone',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MobileOperatorScalarFieldEnum = {
  id: 'id',
  countryId: 'countryId',
  name: 'name',
  nameEn: 'nameEn',
  nameZh: 'nameZh',
  code: 'code',
  logoUrl: 'logoUrl',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PageScalarFieldEnum = {
  id: 'id',
  slug: 'slug',
  pageId: 'pageId',
  title: 'title',
  content: 'content',
  styles: 'styles',
  language: 'language',
  metaTitle: 'metaTitle',
  metaDesc: 'metaDesc',
  isPublished: 'isPublished',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ArticleScalarFieldEnum = {
  id: 'id',
  title: 'title',
  summary: 'summary',
  content: 'content',
  category: 'category',
  imageUrl: 'imageUrl',
  author: 'author',
  publishDate: 'publishDate',
  readCount: 'readCount',
  likeCount: 'likeCount',
  tags: 'tags',
  isPublished: 'isPublished',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BannerScalarFieldEnum = {
  id: 'id',
  title: 'title',
  subtitle: 'subtitle',
  description: 'description',
  imageUrl: 'imageUrl',
  link: 'link',
  position: 'position',
  language: 'language',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  priority: 'priority',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HomeFeatureScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  icon: 'icon',
  imageUrl: 'imageUrl',
  link: 'link',
  type: 'type',
  action: 'action',
  position: 'position',
  color: 'color',
  language: 'language',
  isActive: 'isActive',
  priority: 'priority',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TravelTipScalarFieldEnum = {
  id: 'id',
  title: 'title',
  content: 'content',
  imageUrl: 'imageUrl',
  link: 'link',
  category: 'category',
  isActive: 'isActive',
  language: 'language',
  priority: 'priority',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RefundScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  paymentId: 'paymentId',
  amount: 'amount',
  currency: 'currency',
  reason: 'reason',
  status: 'status',
  processedAt: 'processedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = {
  ADMIN: 'ADMIN',
  CUSTOMER: 'CUSTOMER',
  STAFF: 'STAFF'
};

exports.ProductStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  OUT_OF_STOCK: 'OUT_OF_STOCK',
  DELETED: 'DELETED'
};

exports.OrderStatus = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED',
  PAYMENT_FAILED: 'PAYMENT_FAILED'
};

exports.PaymentStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED'
};

exports.AddressType = {
  SHIPPING: 'SHIPPING',
  BILLING: 'BILLING'
};

exports.AffiliateStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED'
};

exports.ReferralStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  PAID: 'PAID'
};

exports.WithdrawalStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  REJECTED: 'REJECTED'
};

exports.PreSaleSubscriptionStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  UNSUBSCRIBED: 'UNSUBSCRIBED'
};

exports.OrganizationInviteStatus = {
  PENDING: 'PENDING',
  ACCEPTED: 'ACCEPTED',
  REJECTED: 'REJECTED',
  EXPIRED: 'EXPIRED'
};

exports.MarketingCommissionStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  PAID: 'PAID'
};

exports.NotificationType = {
  SYSTEM: 'SYSTEM',
  ORDER: 'ORDER',
  PROMOTION: 'PROMOTION',
  SECURITY: 'SECURITY'
};

exports.TransactionType = {
  DEPOSIT: 'DEPOSIT',
  PAYMENT: 'PAYMENT',
  REFUND: 'REFUND',
  WITHDRAWAL: 'WITHDRAWAL'
};

exports.TransactionStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED'
};

exports.CouponType = {
  PERCENTAGE: 'PERCENTAGE',
  FIXED: 'FIXED'
};

exports.CouponStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  EXPIRED: 'EXPIRED'
};

exports.RefundStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  COMPLETED: 'COMPLETED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Account: 'Account',
  Session: 'Session',
  VerificationToken: 'VerificationToken',
  Product: 'Product',
  ProductVariant: 'ProductVariant',
  ProductParameter: 'ProductParameter',
  Category: 'Category',
  CartItem: 'CartItem',
  WishlistItem: 'WishlistItem',
  Order: 'Order',
  OdooOrderStatus: 'OdooOrderStatus',
  Payment: 'Payment',
  Address: 'Address',
  OrderItem: 'OrderItem',
  Review: 'Review',
  Settings: 'Settings',
  YollooCard: 'YollooCard',
  Esim: 'Esim',
  EsimProfile: 'EsimProfile',
  EsimPlan: 'EsimPlan',
  AffiliateProfile: 'AffiliateProfile',
  AffiliateReferral: 'AffiliateReferral',
  AffiliateWithdrawal: 'AffiliateWithdrawal',
  AffiliateVisit: 'AffiliateVisit',
  PreSaleSubscription: 'PreSaleSubscription',
  AffiliateOrganization: 'AffiliateOrganization',
  OrganizationWithdrawal: 'OrganizationWithdrawal',
  OrganizationCommission: 'OrganizationCommission',
  OrganizationInvite: 'OrganizationInvite',
  PasswordResetToken: 'PasswordResetToken',
  UserLoginHistory: 'UserLoginHistory',
  MarketingCommission: 'MarketingCommission',
  SyncLog: 'SyncLog',
  SyncTask: 'SyncTask',
  Notification: 'Notification',
  Wallet: 'Wallet',
  Transaction: 'Transaction',
  PaymentCard: 'PaymentCard',
  Coupon: 'Coupon',
  UserCoupon: 'UserCoupon',
  PackageUsage: 'PackageUsage',
  RechargeHistory: 'RechargeHistory',
  SocialAccount: 'SocialAccount',
  ProductView: 'ProductView',
  Upload: 'Upload',
  Continent: 'Continent',
  Country: 'Country',
  MobileOperator: 'MobileOperator',
  Page: 'Page',
  Article: 'Article',
  Banner: 'Banner',
  HomeFeature: 'HomeFeature',
  TravelTip: 'TravelTip',
  Refund: 'Refund'
};

/**
 * Create the Client
 */
class PrismaClient {
  constructor() {
    throw new Error(
      `PrismaClient is unable to be run in the browser.
In case this error is unexpected for you, please report it in https://github.com/prisma/prisma/issues`,
    )
  }
}
exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
