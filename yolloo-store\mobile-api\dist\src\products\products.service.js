"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let ProductsService = class ProductsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getCategories(ctx) {
        console.log('Context in getCategories:', ctx);
        const categories = await this.prisma.category.findMany({
            select: {
                id: true,
                name: true,
                description: true,
                image: true,
            },
        });
        const formattedCategories = categories.map(category => ({
            id: category.id,
            name: category.name,
            description: category.description,
            imageUrl: category.image,
        }));
        return { categories: formattedCategories };
    }
    async getProductsByCategory(categoryId, page = 1, pageSize = 20, ctx) {
        console.log('Context in getProductsByCategory:', ctx);
        const skip = (page - 1) * pageSize;
        const [products, total] = await Promise.all([
            this.prisma.product.findMany({
                where: { categoryId },
                skip,
                take: pageSize,
                select: {
                    id: true,
                    name: true,
                    description: true,
                    price: true,
                    images: true,
                    dataSize: true,
                    planType: true,
                    websiteDescription: true,
                    specifications: true,
                    createdAt: true,
                    updatedAt: true,
                },
                orderBy: {
                    createdAt: 'asc',
                },
            }),
            this.prisma.product.count({
                where: { categoryId },
            }),
        ]);
        const formattedProducts = products.map(product => {
            const specs = typeof product.specifications === 'string'
                ? JSON.parse(product.specifications)
                : product.specifications;
            const countries = specs?.countries || [];
            return {
                id: product.id,
                name: product.name,
                description: product.description,
                price: product.price,
                currency: ctx.currency,
                imageUrl: product.images && product.images.length > 0 ? product.images[0] : null,
                dataSize: product.dataSize || 0,
                planType: product.planType || 'Total',
                duration: 30,
                countries: countries,
                rating: 0,
                reviewCount: 0,
            };
        });
        return {
            products: formattedProducts,
            pagination: {
                total,
                page,
                pageSize,
                hasMore: skip + products.length < total,
            },
        };
    }
    async searchProducts(searchDto, ctx) {
        console.log('Context in searchProducts:', ctx);
        const { query, countries, minPrice, maxPrice, dataSize, planType, page = 1, pageSize = 20 } = searchDto;
        const skip = (page - 1) * pageSize;
        const where = {};
        if (query) {
            where.OR = [
                { name: { contains: query, mode: 'insensitive' } },
                { description: { contains: query, mode: 'insensitive' } },
            ];
        }
        if (minPrice !== undefined) {
            where.price = { ...where.price, gte: minPrice };
        }
        if (maxPrice !== undefined) {
            where.price = { ...where.price, lte: maxPrice };
        }
        if (dataSize !== undefined) {
            where.dataSize = { gte: dataSize };
        }
        if (planType) {
            where.planType = planType;
        }
        if (countries) {
            const countryList = countries.split(',').map(c => c.trim());
            where.specifications = {
                path: ['countries'],
                array_contains: countryList,
            };
        }
        const [products, total] = await Promise.all([
            this.prisma.product.findMany({
                where,
                skip,
                take: pageSize,
                select: {
                    id: true,
                    name: true,
                    description: true,
                    price: true,
                    images: true,
                    dataSize: true,
                    planType: true,
                    websiteDescription: true,
                    specifications: true,
                    createdAt: true,
                    updatedAt: true,
                },
                orderBy: {
                    createdAt: 'asc',
                },
            }),
            this.prisma.product.count({ where }),
        ]);
        const formattedProducts = products.map(product => {
            const specs = typeof product.specifications === 'string'
                ? JSON.parse(product.specifications)
                : product.specifications;
            const countries = specs?.countries || [];
            return {
                id: product.id,
                name: product.name,
                description: product.description,
                price: product.price,
                currency: ctx.currency,
                imageUrl: product.images && product.images.length > 0 ? product.images[0] : null,
                dataSize: product.dataSize || 0,
                planType: product.planType || 'Total',
                duration: 30,
                countries: countries,
                rating: 0,
                reviewCount: 0,
            };
        });
        return {
            products: formattedProducts,
            pagination: {
                total,
                page,
                pageSize,
                hasMore: skip + products.length < total,
            },
        };
    }
    async getProductById(productId, ctx) {
        console.log('Context in getProductById:', ctx);
        const product = await this.prisma.product.findUnique({
            where: { id: productId },
            include: {
                variants: true,
                reviews: {
                    take: 5,
                    include: {
                        user: true,
                    },
                },
            },
        });
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        const reviewsWithUserInfo = product.reviews.map(review => ({
            id: review.id,
            rating: review.rating,
            comment: review.comment || '',
            user: {
                name: review.user?.name || 'Anonymous',
                image: review.user?.image || null,
            },
            date: review.createdAt.toISOString(),
        }));
        const formattedVariants = product.variants.map(variant => ({
            id: variant.id,
            attributes: variant.attributes || {
                duration: variant.duration || 30,
                dataSize: 0,
            },
            price: Number(variant.price),
            currency: variant.currency,
        }));
        const specs = typeof product.specifications === 'string'
            ? JSON.parse(product.specifications)
            : product.specifications;
        const countries = specs?.countries || [];
        const specifications = product.specifications ?
            (typeof product.specifications === 'string' ?
                JSON.parse(product.specifications) :
                product.specifications) :
            {};
        return {
            id: product.id,
            name: product.name,
            description: product.description,
            fullDescription: product.websiteDescription || product.description,
            price: product.price,
            currency: ctx.currency,
            images: product.images || [],
            dataSize: product.dataSize || 0,
            planType: product.planType || 'Total',
            duration: 30,
            countries: countries,
            countryCount: countries.length,
            specifications: {
                network: specifications.network || '4G/5G',
                activation: specifications.activation || '即时',
                provider: specifications.provider || '全球网络',
            },
            variants: formattedVariants,
            reviews: reviewsWithUserInfo,
            rating: 0,
            reviewCount: product.reviews.length,
            activationInstructions: '购买后扫描二维码立即激活您的eSIM。',
        };
    }
};
ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ProductsService);
exports.ProductsService = ProductsService;
//# sourceMappingURL=products.service.js.map